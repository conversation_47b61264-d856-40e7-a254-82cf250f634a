#include "speed_control.h"
#include "../config/config.h"
#include "../gps_com/gps_com.h"
#include "../can_com/can_com.h"
#include "../audio_debug_com/audio_debug_com.h"
#include "../out_ports/out_ports.h"
#include "cmsis_os.h"  // For osKernelSysTick()

static int current_speed = 0;
static bool alarm_vel_1 = false;
static bool alarm_vel_2 = false;
static uint32_t alarm_start_time_1 = 0;
static uint32_t alarm_start_time_2 = 0;
static bool beep_on_vel_1 = false;
static bool beep_on_vel_2 = false;
static bool previous_wet_condition = false;
static bool pa6_active = false;

void initialize_speed_control(void) {
    current_speed = 0;
    alarm_vel_1 = false;
    alarm_vel_2 = false;
    alarm_start_time_1 = 0;
    alarm_start_time_2 = 0;
    beep_on_vel_1 = false;
    beep_on_vel_2 = false;
    previous_wet_condition = false;
    pa6_active = false;
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_RESET);
}

void process_speed_control(int speed, bool is_wet_condition) {
    config_file_t *config_file = config_file_get();

    current_speed = speed;

    // Check if condition has changed
    if (is_wet_condition != previous_wet_condition) {
        // Reset states when switching between wet and dry conditions
        alarm_vel_1 = false;
        alarm_vel_2 = false;
        alarm_start_time_1 = 0;
        alarm_start_time_2 = 0;
        beep_on_vel_1 = false;
        beep_on_vel_2 = false;
        set_buzzer(0);
        if (pa6_active) {
            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_RESET);
            pa6_active = false;
        }
        previous_wet_condition = is_wet_condition;
    }

    if (is_wet_condition) {
        process_wet_condition(speed, config_file);
    } else {
        process_dry_condition(speed, config_file);
    }

    // Ensure buzzer state is correct
    if ((is_wet_condition && beep_on_vel_2) || (!is_wet_condition && beep_on_vel_1)) {
        set_buzzer(1);
    } else {
        set_buzzer(0);
    }

    // Ensure PA6 state is correct
    if ((is_wet_condition && speed <= config_file->speed_2_speed_alarms) ||
        (!is_wet_condition && speed <= config_file->speed_1_speed_alarms)) {
        if (pa6_active) {
            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_RESET);
            pa6_active = false;
        }
    }
}

void process_dry_condition(int speed, config_file_t *config_file) {
    uint32_t current_time = osKernelSysTick();

    if (speed > config_file->speed_1_speed_beep) {
        beep_on_vel_1 = true;

        if (speed > config_file->speed_1_speed_alarms) {
            if (alarm_start_time_1 == 0) {
                alarm_start_time_1 = current_time;
            }

            if ((current_time - alarm_start_time_1) >= (config_file->speed_1_seconds_min * 1000)) {
                if (!alarm_vel_1) {
                    alarm_vel_1 = true;
                    send_speed_alarm(101, speed);
                    play_audio_warning(0x0B);

                    if (config_file->speed_1_activate_out2 == 1 && !pa6_active) {
                        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_SET);
                        pa6_active = true;
                    }
                }
            }
        } else {
            alarm_start_time_1 = 0;
            alarm_vel_1 = false;
        }
    } else {
        beep_on_vel_1 = false;
        alarm_start_time_1 = 0;
        alarm_vel_1 = false;
    }
}

void process_wet_condition(int speed, config_file_t *config_file) {
    uint32_t current_time = osKernelSysTick();

    if (speed > config_file->speed_2_speed_beep) {
        beep_on_vel_2 = true;

        if (speed > config_file->speed_2_speed_alarms) {
            if (alarm_start_time_2 == 0) {
                alarm_start_time_2 = current_time;
            }

            if ((current_time - alarm_start_time_2) >= (config_file->speed_2_seconds_min * 1000)) {
                if (!alarm_vel_2) {
                    alarm_vel_2 = true;
                    send_speed_alarm(102, speed);
                    play_audio_warning(0x0A);

                    if (config_file->speed_2_activate_out2 == 1 && !pa6_active) {
                        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_SET);
                        pa6_active = true;
                    }
                }
            }
        } else {
            alarm_start_time_2 = 0;
            alarm_vel_2 = false;
        }
    } else {
        beep_on_vel_2 = false;
        alarm_start_time_2 = 0;
        alarm_vel_2 = false;
    }
}

void send_speed_alarm(int alarm_code, int speed) {
    char alarme[13];
    snprintf(alarme, sizeof(alarme), "ALM,%d,%d*", alarm_code, speed);
    queue_message_put(gps_com_get_tx_messages_queue(), NULL, 0, alarme, strlen(alarme), NULL, 0, 0);
}

void play_audio_warning(uint8_t warning_code) {
    uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF, 0x06, 0x0F, 0x00, warning_code, 0x01, 0xEF };
    queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0, hexBytes_audio_driver, sizeof(hexBytes_audio_driver), NULL, 0, 0);
}

int get_current_speed(void) {
    return current_speed;
}
