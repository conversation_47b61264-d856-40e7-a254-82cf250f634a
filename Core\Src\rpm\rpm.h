/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _RPM_H_
#define _RPM_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#if defined(USE_C8_MCU)
# define RPM_RPM_CH_GPIO_PIN                  GPIO_PIN_2
# define RPM_RPM_CH_GPIO_EXTI_IRQn            EXTI2_IRQn

# define RPM_SPEED_CH_GPIO_PIN                GPIO_PIN_3
# define RPM_SPEED_CH_GPIO_EXTI_IRQn          EXTI3_IRQn
#else
# define RPM_RPM_CH_GPIO_PIN                  GPIO_PIN_8
# define RPM_SPEED_CH_GPIO_PIN                GPIO_PIN_9
# define RPM_SPEED_RPM_CHs_GPIO_EXTI_IRQn     EXTI9_5_IRQn
#endif // USE_C8_MCU

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
void rpm_init(void);

void rpm_rpm_cb(void);
void rpm_speed_cb(void);

void rpm_time_proc(void);

void rpm_latch(void);
void rpm_calculate(void);

uint32_t rpm_get_rpm_freq(void);
uint32_t rpm_get_speed_freq(void);

uint32_t rpm_get_odometer_ablosute_cnt(void);
void rpm_set_odometer_ablosute_cnt(uint64_t value);

#endif // _RPM_H_
