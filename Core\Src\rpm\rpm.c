/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "rpm.h"
#include "../config/config.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define RPM_EXTI_PREEM_PRIO                   (5)
#define RPM_EXTI_SUB_PRIO                     (0)

#define RPM_TIMER_TICK_WEIGHT_US              ((1.0/((float)SystemCoreClock))*1000.0*1000.0) // in us
#define RPM_IDLE_TIMEOUT_MS                   (2*1000)

/****************************************************************************
 * Private Types
 ****************************************************************************/
typedef struct {
  uint32_t cntr_prev;         // dwt counter prev value
  uint32_t cntr;              // dwt counter
  uint32_t latched_cntr_prev; // latched dwt counter prev value
  uint32_t latched_cntr;      // latched dwt counter
  uint32_t idle_timeout_ms;   // idle timeout for signal
  uint32_t period_us;         // period in us
  uint32_t freq_hz_x10;       // freq in hz x10
  uint32_t freq_hz;           // freq in hz
} rpm_ch_t;

typedef struct {
  rpm_ch_t rpm_ch;
  rpm_ch_t speed_ch;
  uint32_t odometer_absolute_cntr;
} rpm_t;

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static void rpm_time_proc_impl(rpm_ch_t* ch_p);
static uint32_t rpm_freq_calculate(rpm_ch_t* ch_p);

/****************************************************************************
 * Private Data
 ****************************************************************************/
static rpm_t rpm;

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/
static void rpm_time_proc_impl(rpm_ch_t* ch_p)
{
  if (ch_p == NULL) {
    return;
  }
  if (ch_p->idle_timeout_ms > 0) {
    ch_p->idle_timeout_ms--;
    if (ch_p->idle_timeout_ms == 0) {
      // no any signal on the input
      rpm.rpm_ch.cntr_prev           = 0;
      rpm.rpm_ch.latched_cntr_prev   = 0;
      rpm.rpm_ch.cntr                = 0;
      rpm.rpm_ch.latched_cntr        = 0;

      rpm.speed_ch.cntr_prev         = 0;
      rpm.speed_ch.latched_cntr_prev = 0;
      rpm.speed_ch.cntr              = 0;
      rpm.speed_ch.latched_cntr      = 0;
    }
  }
}

static uint32_t rpm_freq_calculate(rpm_ch_t* ch_p)
{
  if (ch_p == NULL) {
    return 0;
  }
  if ((ch_p->latched_cntr_prev == 0) || (ch_p->latched_cntr == 0))  {
    ch_p->period_us   = 0;
    ch_p->freq_hz_x10 = 0;
    ch_p->freq_hz     = 0;
    return 0;
  }

  // calc period in dwt counter
  uint32_t period = 0;
  if (ch_p->latched_cntr >= ch_p->latched_cntr_prev) {
    period = ch_p->latched_cntr - ch_p->latched_cntr_prev;
  } else {
    period = (0xffffffff - ch_p->latched_cntr_prev) + ch_p->latched_cntr;
  }

  // period in us
  float period_us = ((float)period)*RPM_TIMER_TICK_WEIGHT_US;
  ch_p->period_us = (uint32_t)period_us;

  // freq
  float freq        = 1.0/(period_us/1000000.0);
  ch_p->freq_hz_x10 = (uint32_t)(freq*10.0);
  uint32_t freq_hz  = ch_p->freq_hz_x10/10;
  if ((ch_p->freq_hz_x10%10) >= 5) {
    freq_hz++;
  }
  return freq_hz;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
void rpm_init(void)
{
	//ALTERADO
  // microseconds counter initialization
//  CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk; // enable counter
//	DWT->CTRL        |= DWT_CTRL_CYCCNTENA_Msk;     // start counter

	//ALTERADO
  // clock input gpio init
//  GPIO_InitTypeDef GPIO_InitStruct;
//  memset(&GPIO_InitStruct, 0, sizeof(GPIO_InitTypeDef));
//  GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;
//  GPIO_InitStruct.Pull = GPIO_PULLDOWN;

  //ALTERADO
//  // RPM channel
//  RPM_RPM_CH_GPIO_CLK_ENABLE();
//  GPIO_InitStruct.Pin  = RPM_RPM_CH_GPIO_PIN;
//  HAL_GPIO_Init(RPM_RPM_CH_GPIO_PORT, &GPIO_InitStruct);
//
//  // SPEED channel
//  RPM_SPEED_CH_GPIO_CLK_ENABLE();
//  GPIO_InitStruct.Pin  = RPM_SPEED_CH_GPIO_PIN;
//  HAL_GPIO_Init(RPM_SPEED_CH_GPIO_PORT, &GPIO_InitStruct);

//  HAL_NVIC_SetPriority(RPM_SPEED_RPM_CHs_GPIO_EXTI_IRQn, RPM_EXTI_PREEM_PRIO, RPM_EXTI_SUB_PRIO);
//  HAL_NVIC_EnableIRQ(RPM_SPEED_RPM_CHs_GPIO_EXTI_IRQn);
}

void rpm_rpm_cb(void)
{
  rpm.rpm_ch.cntr_prev       = rpm.rpm_ch.cntr;
  rpm.rpm_ch.cntr            = DWT->CYCCNT;
  rpm.rpm_ch.idle_timeout_ms = RPM_IDLE_TIMEOUT_MS;
}

void rpm_speed_cb(void)
{
  rpm.speed_ch.cntr_prev       = rpm.speed_ch.cntr;
  rpm.speed_ch.cntr            = DWT->CYCCNT;
  rpm.speed_ch.idle_timeout_ms = RPM_IDLE_TIMEOUT_MS;
}

void rpm_time_proc(void)
{
  rpm_time_proc_impl(&rpm.rpm_ch);
  rpm_time_proc_impl(&rpm.speed_ch);
}

void rpm_latch(void)
{
  __disable_irq();
  rpm.rpm_ch.latched_cntr_prev   = rpm.rpm_ch.cntr_prev;
  rpm.rpm_ch.latched_cntr        = rpm.rpm_ch.cntr;

  rpm.speed_ch.latched_cntr_prev = rpm.speed_ch.cntr_prev;
  rpm.speed_ch.latched_cntr      = rpm.speed_ch.cntr;
  __enable_irq();
}

void rpm_calculate(void)
{
  // calculate freq
  uint32_t rpm_freq_hz   = rpm_freq_calculate(&rpm.rpm_ch);
  uint32_t speed_freq_hz = rpm_freq_calculate(&rpm.speed_ch);

////  // for test only
////  uint32_t rpm_freq_hz   = rpm.rpm_ch.freq_hz;
////  uint32_t speed_freq_hz = rpm.speed_ch.freq_hz;

  // update values in struct
  __disable_irq();
  rpm.rpm_ch.freq_hz          = rpm_freq_hz;
  rpm.speed_ch.freq_hz        = speed_freq_hz;
  rpm.odometer_absolute_cntr += speed_freq_hz;
  __enable_irq();
}

uint32_t rpm_get_rpm_freq(void)
{
  return rpm.rpm_ch.freq_hz;
}

uint32_t rpm_get_speed_freq(void)
{
  return rpm.speed_ch.freq_hz;
}

uint32_t rpm_get_odometer_ablosute_cnt(void)
{
  return rpm.odometer_absolute_cntr;
}

void rpm_set_odometer_ablosute_cnt(uint64_t value)
{
  rpm.odometer_absolute_cntr = value;
}
