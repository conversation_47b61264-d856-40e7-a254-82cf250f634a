/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _CAN_UART_H_
#define _CAN_UART_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define CAN_UART_RX_BUFF_SIZE       500
#define CAN_UART_FINAL_BUFF_SIZE    500

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
UART_HandleTypeDef* can_uart_get(void);

bool can_uart_init(void (*rx_cpl_cb)(uint8_t* data_p, size_t data_len));
bool can_uart_reinit();
void can_uart_msp_init(void);
void can_uart_msp_deinit(void);

bool can_uart_is_rx_started(void);
void can_uart_rx_cpl_cb(void);

bool can_uart_send(uint8_t* data, size_t data_len, void (*tx_cpl_cb)(void));
void can_uart_tx_cpl_cb(void);

void can_uart_err_cb(void);

void CAN_UART_DMA_RX_IRQHandler(void);
void CAN_UART_DMA_TX_IRQHandler(void);
void CAN_UART_IRQHandler(void);

#endif // _CAN_UART_H_
