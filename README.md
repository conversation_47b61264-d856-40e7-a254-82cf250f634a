Projeto do firmware do STR1010+

Changelog:

[1.1.8] - 10/12/2024 (<PERSON>)
- leitor CAN fica ligado direto, independente da ignição.

[1.1.7] - 29/10/2024 (<PERSON>)
- bug fix, buffer do alarme de tombamento insuficiente para ângulos com 3 ou mais caracteres.

[1.1.6] - 14/10/2024 (<PERSON>)
- inclusão das configurações de motorista não identificado e mecanismo de corte de motor ocioso nos
comandos de set e get múltiplas configurações.

[1.1.5] - 12/10/2024 (<PERSON>)
- inclusão do mecanismo de corte de motor ocioso com OUT4 (aciona OUT4, manda alarme e dispara áudio)
- inclusão do alarme de corte de motor ocioso "ALM,114,1*".
- inclusão do comando para configurar o corte de motor ocioso "CMD,CFG,ENGCUT,0|1,180,10*".

[1.1.4] - 23/09/2024 (<PERSON>)
- inclusão do alarme de motorista não identificado "ALM,113,{velocidade}*".
- inclusão do comando para configurar o alarme de motorista não identificado "CMD,CFG,DRIVER,0|1*".

[1.1.3] - 21/09/2024 (Pedro)
- mais ajustes no teclado para dar agilidade em relação à ignição e evitar filas enormes de áudio.
- normalização da mensagem de texto para o teclado, removendo espaços extras e limitando a 32char.

[1.1.2] - 20/09/2024 (Pedro)
- ajustes na função do teclado para usar o delay de 2seg non blocking ao invés de osDelay(2000).
- inclusão de mensagem "TOSERVER,STARTUP*" quando inicia ou reinicia o sistema.

[1.1.1] - 18/09/2024 (Pedro)
- adicionada condição para não executar o comando de sms para o teclado se configurado para RFID.

[1.1.0] - 17/09/2024 (Pedro)
- finalizada implementação do watchdog.

[1.0.9] - 17/09/2024 (Pedro)
- incluída resposta do comando FWGET na porta de debug UART4.

[1.0.8] - 17/09/2024 (Pedro)
- ajuste na resposta do comando GEOGET para mandar na porta de debug (UART4) as duas respostas separadamente,
GEOC e GEOR. Foram usados dois buffers separados, um para cara resposta separada.

[1.0.7] - 15/09/2024 (Pedro)
- adiciona comando CMD,CFGSET com todos os parâmetros principais de configuração em um só comando, para
viabilizar o uso do software de configuração.

[1.0.6] - 14/09/2024 (Pedro)
- adiciona todos os dados de cerca na resposta do GEOGET, padroniza o type das coordenadas como double,
altera o type do raio da cerca circular de float para uint16_t e aumenta buffer da resposta para 900.
- adiciona todos os dados de configuração que faltavam na resposta do CFGGET e muda buffer para 512.

[1.0.5] - 13/09/2024 (Pedro)
- adiciona todos os dados de TPMS na resposta do TPMSGET e aumenta buffer da resposta de 720 para 1100
- remove trailing comma das respostas do DRVGET
- reduz o limite de motoristas por resposta do DRVGET de 83 para 80.
- separa a resposta do GEOGET em duas (GEOC para circular e GEOR para retangular, adicionando os parametros
in_out e speed_max de cada uma. exemplo:
TOSERVER,GEOC,0029,1,40,9999,1,70*
TOSERVER,GEOR,1001,1,50,9998,1,70*
- adicionado comando CMD,CFGGET* que retornará vários parâmetros de configuração correntes, no formato:
TOSERVER,RPMVD,{rpm_green_min},{rpm_green_max},RPMAM,{rpm_yellow_min},{rpm_yellow_max},RPMVM,{rpm_red_min},
{rpm_red_max},DHL,{downhill_rpm_max},{downhill_speed_min},{downhill_seconds_min},RPMMAX,{rpm_max},ENGTEMP,
{temperature_engine_max},TURBO,{turbo_pressure_max},VEL1,{speed_1_speed_beep},{speed_1_speed_alarms},
{speed_1_seconds_min},{speed_1_activate_out2},VEL2,{speed_2_speed_beep},{speed_2_speed_alarms},
{speed_2_seconds_min},{speed_2_activate_out2}*

[1.0.4] - 12/09/2024 (Pedro)
- voltei DMA no GPS, tilt e audio/log

[1.0.3] - 11/09/2024 (Pedro)
- Revertido GPS, Audio/Debug e Tilt sensor de DMA para interrupt.
- Removido codigo da cerca no alarme de excesso de velocidade em cerca.

[1.0.2] - 10/09/2024 (Pedro)
- removida rotina de alarmes de velocidade de dentro do can_com.c e unificada no speed_control, uma rotina
só para velocidade CAN e GPS.
- separados os arquivos speed_control e geofence em pastas diferentes.

[1.0.1] - 10/09/2024 (Pedro)
- Comando para verificar versão do firmware (CMD,GETFW*)
- Envio do código da cerca junto com a velocidade na mensagem de alarme de excesso de velocidade em cerca
(ALM,103,71|9999*)