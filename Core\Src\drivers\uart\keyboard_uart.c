/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "keyboard_uart.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define keyboard_uart_IRQHandler         USART3_IRQHandler
/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static void (*rx_cpl_cb_p)(uint8_t *data_p, size_t data_len) = NULL;
static uint8_t rx_data[KEYBOARD_UART_RX_BUFF_SIZE];

/****************************************************************************
 * Public Data
 ****************************************************************************/
UART_HandleTypeDef huart3;
/****************************************************************************
 * Private Functions
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void) {
	__disable_irq();

	HAL_StatusTypeDef status = HAL_UARTEx_ReceiveToIdle_IT(&huart3, rx_data, sizeof(rx_data));

	__enable_irq();

	return status;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
UART_HandleTypeDef* keyboard_uart_get(void) {
	return &huart3;
}

bool keyboard_uart_init(void (*rx_cpl_cb)(uint8_t *data_p, size_t data_len)) {

	huart3.Instance = USART3;
	huart3.Init.BaudRate = 9600;
	huart3.Init.WordLength = UART_WORDLENGTH_8B;
	huart3.Init.StopBits = UART_STOPBITS_2;
	huart3.Init.Parity = UART_PARITY_NONE;
	huart3.Init.Mode = UART_MODE_TX_RX;
	huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	huart3.Init.OverSampling = UART_OVERSAMPLING_16;
	if (HAL_UART_Init(&huart3) != HAL_OK) {
		Error_Handler();
	}
//  // start rx
	rx_cpl_cb_p = rx_cpl_cb;
	if (uart_start_rx() != HAL_OK) {
		return false;
	}

	return true;
}

bool keyboard_uart_reinit() {

	// deinit usart
	if (HAL_UART_DeInit(&huart3) != HAL_OK) {
		return false;
	}

	// apply new setings
	huart3.Instance = USART3;
	huart3.Init.BaudRate = 9600;
	huart3.Init.WordLength = UART_WORDLENGTH_8B;
	huart3.Init.StopBits = UART_STOPBITS_2;
	huart3.Init.Parity = UART_PARITY_NONE;
	huart3.Init.Mode = UART_MODE_TX_RX;
	huart3.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	huart3.Init.OverSampling = UART_OVERSAMPLING_16;
	if (HAL_UART_Init(&huart3) != HAL_OK) {
		Error_Handler();
		return false;
	}

	// start rx
	if (uart_start_rx() != HAL_OK) {
		return false;
	}

	return true;
}

bool keyboard_uart_send(uint8_t *data_p, size_t data_len) {
	if ((data_p == NULL) || (data_len == 0)) {
		return false;
	}
	if (HAL_UART_Transmit_IT(&huart3, data_p, data_len) != HAL_OK) {
		return false;
	}
	return true;
}

bool keyboard_uart_is_rx_started(void) {
	if ((huart3.RxState == HAL_UART_STATE_BUSY_RX)
			&& (huart3.hdmarx->Instance->NDTR < KEYBOARD_UART_RX_BUFF_SIZE)) {
		return true;
	}
	return false;
}

void keyboard_uart_rx_cpl_cb(void) {
	if (rx_cpl_cb_p != NULL) {
		// calculate rx len
		size_t rx_len = huart3.hdmarx->Instance->NDTR;
		if (rx_len > KEYBOARD_UART_RX_BUFF_SIZE) {
			rx_len = KEYBOARD_UART_RX_BUFF_SIZE;
		}
		rx_len = KEYBOARD_UART_RX_BUFF_SIZE - rx_len;

		// notify about new data
		if (rx_len > 0) { // check to prevent execute cp second time
			rx_cpl_cb_p(rx_data, rx_len);

			memset(rx_data,0,sizeof rx_data);
		}
	}

	// start rx again
	uart_start_rx();
}

void keyboard_uart_err_cb(void) {
	// abort
	__HAL_UART_FLUSH_DRREGISTER(&huart3);
	HAL_UART_Abort(&huart3);

	// do tx_cpl_cb to unlock any waiting tasks
	keyboard_uart_tx_cpl_cb();

	// start rx again
	uart_start_rx();
}

void keyboard_uart_IRQHandler(void) {
	// check for idle irq
	bool is_idle = false;
	if (__HAL_UART_GET_FLAG(&huart3, UART_FLAG_IDLE)) {
		is_idle = true;
	}

	// irq processing
	HAL_UART_IRQHandler(&huart3);

	// process idle irq
	if (is_idle) {
		__HAL_UART_CLEAR_IDLEFLAG(&huart3);
		HAL_UART_AbortReceive(&huart3);
		HAL_UART_RxCpltCallback(&huart3);
	}
}
