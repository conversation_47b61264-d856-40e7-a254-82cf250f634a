/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _AUDIO_DEBUG_COMMUNICATION_H_
#define _AUDIO_DEBUG_COMMUNICATION_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
bool audio_debug_com_init(void);
void audio_debug_com_time_proc(void);
bool audio_debug_com_is_idle(void);

osMessageQId audio_debug_com_get_tx_messages_queue(void);

void audio_debug_com_schedule_ignition_on_note();

void audio_debug_com_execute_log_gps_cmd(uint8_t* data_p, size_t data_len, uint8_t src_type, void (*ack_send_func_p)(const char* ack_p));
void audio_debug_com_execute_log_can_cmd(uint8_t* data_p, size_t data_len, uint8_t src_type, void (*ack_send_func_p)(const char* ack_p));
void audio_debug_com_execute_log_fm_cmd(uint8_t* data_p, size_t data_len, uint8_t src_type, void (*ack_send_func_p)(const char* ack_p));
void audio_debug_com_execute_log_tilt_cmd(uint8_t* data_p, size_t data_len, uint8_t src_type, void (*ack_send_func_p)(const char* ack_p));
void audio_debug_com_execute_log_off_cmd(uint8_t* data_p, size_t data_len, uint8_t src_type, void (*ack_send_func_p)(const char* ack_p));
void audio_debug_com_clr_log(void);

void audio_debug_com_write(uint8_t* data_p);

void audio_debug_com_send_log(uint8_t* data_p, size_t data_len, uint8_t uart_num, bool is_hex);

#endif // _AUDIO_DEBUG_COMMUNICATION_H_
