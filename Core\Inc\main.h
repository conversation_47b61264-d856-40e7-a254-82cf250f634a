/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.h
 * @brief          : Header for main.c file.
 *                   This file contains the common defines of the application.
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2024 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include <stddef.h>
#include <stdarg.h>
#include <stdio.h>
#include "cmsis_os.h"
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

// PERIPHERAL RX START
#define PERIPHERAL_GPS_RX_START                     ""
#define PERIPHERAL_GPS_RX_START_LEN                 strlen(PERIPHERAL_GPS_RX_START)

#define PERIPHERAL_TPMS_RX_START                    ""
#define PERIPHERAL_TPMS_RX_START_LEN                strlen(PERIPHERAL_TPMS_RX_START)

#define PERIPHERAL_AUDIO_DEBUG_RX_START             ""
#define PERIPHERAL_AUDIO_DEBUG_RX_START_LEN         strlen(PERIPHERAL_AUDIO_DEBUG_RX_START)

#define PERIPHERAL_TILT_SENSOR_RX_START             ""
#define PERIPHERAL_TILT_SENSOR_RX_START_LEN         strlen(PERIPHERAL_TILT_SENSOR_RX_START)

// PERIPHERAL UART NUM
#define PERIPHERAL_UART_MIN                         (2)
#define PERIPHERAL_UART_MAX                         (5)

#define PERIPHERAL_UART_GPS_NUM                     (1)
#define PERIPHERAL_UART_CAN_NUM                     (2)
#define PERIPHERAL_UART_KEYBOARD_NUM                (3)
#define PERIPHERAL_UART_AUDIO_DEBUG_NUM             (4)
#define PERIPHERAL_UART_TPMS_NUM                    (5)
#define PERIPHERAL_UART_TILT_SENSOR_NUM             (6)

// COMMANDS
#define PERIPHERAL_CMD_START_LEN                    (4)

#define PERIPHERAL_CMD_END                          ""
#define PERIPHERAL_CMD_END_LEN                      strlen(PERIPHERAL_CMD_END)

#define PERIPHERAL_CMD_WRITE_DATA                   "[W"
#define PERIPHERAL_CMD_WRITE_DATA_UART_NUM_OFFS     (2)
#define PERIPHERAL_CMD_WRITE_DATA_DATA_OFFS         (4)

#define PERIPHERAL_CMD_SET_UART                     "[S"
#define PERIPHERAL_CMD_SET_UART_UART_NUM_OFFS       (2)
#define PERIPHERAL_CMD_SET_UART_DATA_OFFS           (4)

#define PERIPHERAL_CMD_SET_RPM                      "CMD,CFG,RPM,"
#define PERIPHERAL_CMD_SET_RPM_LEN                  strlen(PERIPHERAL_CMD_SET_RPM)

#define PERIPHERAL_CMD_SET_KEYBOARD                 "CMD,CFG,KEYBOARD,"
#define PERIPHERAL_CMD_SET_KEYBOARD_LEN             strlen(PERIPHERAL_CMD_SET_KEYBOARD)

#define PERIPHERAL_CMD_SET_RPM_DEF                  "CMD,CFG,RPMDEF,"
#define PERIPHERAL_CMD_SET_RPM_DEF_LEN              strlen(PERIPHERAL_CMD_SET_RPM_DEF)

#define PERIPHERAL_CMD_SET_TURBO_DEF                "CMD,CFG,TURBODEF,"
#define PERIPHERAL_CMD_SET_TURBO_DEF_LEN            strlen(PERIPHERAL_CMD_SET_TURBO_DEF)

#define PERIPHERAL_CMD_SET_CLEAR_MERMORY            "CMD,MEM_CLR*"
#define PERIPHERAL_CMD_SET_CLEAR_MERMORY_LEN        strlen(PERIPHERAL_CMD_SET_CLEAR_MERMORY)

#define PERIPHERAL_CMD_SET_RPM_LIMIT                "CMD,CFG,RPMLIM,"
#define PERIPHERAL_CMD_SET_RPM_LIMIT_LEN            strlen(PERIPHERAL_CMD_SET_RPM_LIMIT)

#define PERIPHERAL_CMD_SET_TURBO_LIMIT              "CMD,CFG,TURBO,"
#define PERIPHERAL_CMD_SET_TURBO_LIMIT_LEN          strlen(PERIPHERAL_CMD_SET_TURBO_LIMIT)

#define PERIPHERAL_CMD_SET_FUEL_OUT             	"CMD,CFG,FUELOUT,"
#define PERIPHERAL_CMD_SET_FUEL_OUT_LEN          	strlen(PERIPHERAL_CMD_SET_FUEL_OUT)

#define PERIPHERAL_CMD_SET_FUEL_IN              	"CMD,CFG,FUELIN,"
#define PERIPHERAL_CMD_SET_FUEL_IN_LEN          	strlen(PERIPHERAL_CMD_SET_FUEL_IN)

#define PERIPHERAL_CMD_SET_UART              		"CMD,CFG,UART"
#define PERIPHERAL_CMD_SET_UART_LEN          		strlen(PERIPHERAL_CMD_SET_UART)

#define PERIPHERAL_CMD_SET_OUT              		"CMD,OUT"
#define PERIPHERAL_CMD_SET_OUT_LEN          		strlen(PERIPHERAL_CMD_SET_OUT)

#define PERIPHERAL_CMD_SET_GEOR             		"CMD,GEOR,"
#define PERIPHERAL_CMD_SET_GEOR_LEN          		strlen(PERIPHERAL_CMD_SET_GEOR)

#define PERIPHERAL_CMD_SET_GEOC             		"CMD,GEOC,"
#define PERIPHERAL_CMD_SET_GEOC_LEN          		strlen(PERIPHERAL_CMD_SET_GEOC)

#define PERIPHERAL_CMD_SET_CFG	             		"CMD,CFGSET,"
#define PERIPHERAL_CMD_SET_CFG_LEN          		strlen(PERIPHERAL_CMD_SET_CFG)

#define PERIPHERAL_CMD_SMS  	          			"CMD,SMS,"
#define PERIPHERAL_CMD_SMS_LEN          			strlen(PERIPHERAL_CMD_SMS)

#define PERIPHERAL_CMD_SET_GEODEL             		"CMD,GEODEL"
#define PERIPHERAL_CMD_SET_GEODEL_LEN          		strlen(PERIPHERAL_CMD_SET_GEODEL)

#define PERIPHERAL_CMD_SET_GEOGET             		"CMD,GEOGET*"
#define PERIPHERAL_CMD_SET_GEOGET_LEN          		strlen(PERIPHERAL_CMD_SET_GEOGET)

#define PERIPHERAL_CMD_SET_VEL              		"CMD,CFG,VEL"
#define PERIPHERAL_CMD_SET_VEL_LEN          		strlen(PERIPHERAL_CMD_SET_VEL)

#define PERIPHERAL_CMD_MP3              			"CMD,MP3,"
#define PERIPHERAL_CMD_MP3_LEN          			strlen(PERIPHERAL_CMD_MP3)

#define PERIPHERAL_CMD_SET_TPMS_GET              	"CMD,TPMSGET*"
#define PERIPHERAL_CMD_SET_TPMS_GET_LEN          	strlen(PERIPHERAL_CMD_SET_TPMS_GET)

#define PERIPHERAL_CMD_SET_CONFIG_GET              	"CMD,CFGGET*"
#define PERIPHERAL_CMD_SET_CONFIG_GET_LEN          	strlen(PERIPHERAL_CMD_SET_CONFIG_GET)

#define PERIPHERAL_CMD_SET_TXT_UART              	"CMD,CFG,TXT,UART"
#define PERIPHERAL_CMD_SET_TXT_UART_LEN          	strlen(PERIPHERAL_CMD_SET_TXT_UART)

#define PERIPHERAL_CMD_SET_ENGTEMP_LIMIT            "CMD,CFG,ENGTEMP,"
#define PERIPHERAL_CMD_SET_ENGTEMP_LIMIT_LEN        strlen(PERIPHERAL_CMD_SET_ENGTEMP_LIMIT)

#define PERIPHERAL_CMD_CLR_RPM                      "CMD,CFG,RPM_CLR"
#define PERIPHERAL_CMD_CLR_RPM_LEN                  strlen(PERIPHERAL_CMD_CLR_RPM)

#define PERIPHERAL_CMD_SET_SPEED_CONVERSION         "CMD,CFG,SPEED,"
#define PERIPHERAL_CMD_SET_SPEED_CONVERSION_LEN     strlen(PERIPHERAL_CMD_SET_SPEED_CONVERSION)
#define PERIPHERAL_CMD_SET_SPEED_CONVERSION_MIN_LEN (PERIPHERAL_CMD_SET_SPEED_CONVERSION_LEN+1)

#define PERIPHERAL_CMD_SET_ADD_TPMS		        	"CMD,TPMS,"
#define PERIPHERAL_CMD_SET_ADD_TPMS_LEN     		strlen(PERIPHERAL_CMD_SET_ADD_TPMS)

#define PERIPHERAL_CMD_SET_DELETE_TPMS		        "CMD,TPMSDEL,"
#define PERIPHERAL_CMD_SET_DELETE_TPMS_LEN     		strlen(PERIPHERAL_CMD_SET_DELETE_TPMS)

#define PERIPHERAL_CMD_SET_ADD_DRIVER		        "CMD,DRVADD,"
#define PERIPHERAL_CMD_SET_ADD_DRIVER_LEN     		strlen(PERIPHERAL_CMD_SET_ADD_DRIVER)
#define PERIPHERAL_CMD_SET_ADD_DRIVER_MIN_LEN 		(PERIPHERAL_CMD_SET_ADD_DRIVER_LEN+1)

#define PERIPHERAL_CMD_SET_DELETE_DRIVER		    "CMD,DRVDEL,"
#define PERIPHERAL_CMD_SET_DELETE_DRIVER_LEN     	strlen(PERIPHERAL_CMD_SET_DELETE_DRIVER)

#define PERIPHERAL_CMD_SET_GET_DRIVER		    	"CMD,DRVGET*"
#define PERIPHERAL_CMD_SET_GET_DRIVER_LEN     		strlen(PERIPHERAL_CMD_SET_GET_DRIVER)

#define PERIPHERAL_CMD_SET_GET_ACTIVITY		    	"CMD,ACTGET*"
#define PERIPHERAL_CMD_SET_GET_ACTIVITY_LEN     	strlen(PERIPHERAL_CMD_SET_GET_ACTIVITY)

#define PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE        	"CMD,ACTADD,"
#define PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE_LEN     	strlen(PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE)
#define PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE_MIN_LEN 	(PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE_LEN+1)

#define PERIPHERAL_CMD_SET_DELETE_ACTIVITY_CODE		    "CMD,ACTDEL,"
#define PERIPHERAL_CMD_SET_DELETE_ACTIVITY_CODE_LEN    	strlen(PERIPHERAL_CMD_SET_DELETE_ACTIVITY_CODE)

#define PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE         "CMD,CFG,ROLL,"
#define PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE_LEN     strlen(PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE)
#define PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE_MIN_LEN (PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE_LEN+1)

#define PERIPHERAL_CMD_SET_PULSE                        "CMD,CFG,PULSE,"
#define PERIPHERAL_CMD_SET_PULSE_LEN                    strlen(PERIPHERAL_CMD_SET_PULSE)

#define PERIPHERAL_CMD_SET_DHL                          "CMD,CFG,DHL,"
#define PERIPHERAL_CMD_SET_DHL_LEN                      strlen(PERIPHERAL_CMD_SET_DHL)

#define PERIPHERAL_CMD_SET_DRIVER_ALARM                 "CMD,CFG,DRIVER,"
#define PERIPHERAL_CMD_SET_DRIVER_ALARM_LEN             strlen(PERIPHERAL_CMD_SET_DRIVER_ALARM)

#define PERIPHERAL_CMD_SET_ENGINE_CUT                   "CMD,CFG,ENGCUT,"
#define PERIPHERAL_CMD_SET_ENGINE_CUT_LEN               strlen(PERIPHERAL_CMD_SET_ENGINE_CUT)

#define PERIPHERAL_CMD_CLR_ODO                          "ODO_CLR"
#define PERIPHERAL_CMD_CLR_ODO_LEN                      strlen(PERIPHERAL_CMD_CLR_ODO)

#define PERIPHERAL_CMD_CLR_TURBO                        "CMD,CFG,TURBO_CLR*"
#define PERIPHERAL_CMD_CLR_TURBO_LEN                    strlen(PERIPHERAL_CMD_CLR_TURBO)

#define PERIPHERAL_CMD_CLR_ENG                          "CMD,CFG,ENG_CLR"
#define PERIPHERAL_CMD_CLR_ENG_LEN                      strlen(PERIPHERAL_CMD_CLR_ENG)

#define PERIPHERAL_CMD_REBOOT                           "CMD,REBOOT*"
#define PERIPHERAL_CMD_REBOOT_LEN                       strlen(PERIPHERAL_CMD_REBOOT)

#define PERIPHERAL_CMD_LOG_GPS                          "LOG_GPS"
#define PERIPHERAL_CMD_LOG_GPS_LEN                      strlen(PERIPHERAL_CMD_LOG_GPS)

#define PERIPHERAL_CMD_LOG_CAN                          "LOG_CAN"
#define PERIPHERAL_CMD_LOG_CAN_LEN                      strlen(PERIPHERAL_CMD_LOG_CAN)

#define PERIPHERAL_CMD_LOG_FM                           "LOG_KEYBOARD"
#define PERIPHERAL_CMD_LOG_FM_LEN                       strlen(PERIPHERAL_CMD_LOG_FM)

#define PERIPHERAL_CMD_LOG_TILT                         "LOG_TILT"
#define PERIPHERAL_CMD_LOG_TILT_LEN                     strlen(PERIPHERAL_CMD_LOG_TILT)

#define PERIPHERAL_CMD_LOG_OFF                          "LOG_OFF"
#define PERIPHERAL_CMD_LOG_OFF_LEN                      strlen(PERIPHERAL_CMD_LOG_OFF)

#define PERIPHERAL_CMD_SET_IN				            "CMD,CFG,IN,"
#define PERIPHERAL_CMD_SET_IN_LEN                       strlen(PERIPHERAL_CMD_SET_IN)

#define PERIPHERAL_CMD_GET_FW                   		"CMD,FWGET*"
#define PERIPHERAL_CMD_GET_FW_LEN                		strlen(PERIPHERAL_CMD_GET_FW)

// COMA5PWRON notification
#define IGNITION_ON_NOTE_TIMEOUT_MS						(2000)

// CAN POWER default state -----------------------------------------------------
// 0 - CAN power default OFF
// 1 - CAN power default ON
#define CAN_POWER_DEFAULT_STATE                    (0)
// -----------------------------------------------------------------------------

// Speed conversion default value
#define SPEED_CONVERSION_DEFAULT                   (0) // (1)

// rollover alarm angle default value
#define ROLLOVER_ALARM_ANGLE_DEFAULT               (40)

// software watchdog
#define SOFTWARE_WATCHDOG_TIMEOUT_S_DEFAULT        (3)
#define SOFTWARE_WATCHDOG_OFFSET_MS                (1500)
#define HUB_READY_TIMEOUT_S_DEFAULT                (1)

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */
/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
