/****************************************************************************
 *   Copyright (C) 2020.01.22. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _GPIO_EXTI_H_
#define _GPIO_EXTI_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin);

void EXTI0_IRQHandler(void);
void EXTI1_IRQHandler(void);
void EXTI2_IRQHandler(void);
void EXTI3_IRQHandler(void);

#endif // _GPIO_EXTI_H_
