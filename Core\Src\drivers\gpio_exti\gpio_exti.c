/****************************************************************************
 *   Copyright (C) 2020.01.22. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "gpio_exti.h"
#include "../../rpm/rpm.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Private Data
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/**
  * @brief  EXTI line detection callbacks.
  * @param  GPIO_Pin: Specifies the pins connected EXTI line
  * @retval None
  */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
  if (GPIO_Pin == RPM_RPM_CH_GPIO_PIN) {
    rpm_rpm_cb();
  } else if (GPIO_Pin == RPM_SPEED_CH_GPIO_PIN) {
    rpm_speed_cb();
  }
}


void EXTI2_IRQHandler(void)
{
  HAL_GPIO_EXTI_IRQHandler(RPM_RPM_CH_GPIO_PIN);
}

void EXTI3_IRQHandler(void)
{
  HAL_GPIO_EXTI_IRQHandler(RPM_SPEED_CH_GPIO_PIN);
}
