/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "audio_debug_com.h"
#include "../drivers/uart/audio_debug_uart.h"
#include "../gps_com/gps_com.h"
#include "../queue_mail/queue_mail.h"
#include "../queue_message/queue_message.h"
#include "../config/config.h"
#include "../watchdog/watchdog.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define AUDIO_DEBUG_COM_RX_MAILS_QUEUE_SIZE         (5)  // Hold incoming mails from audio_debug till RX task processing it. So, should be not big(processing is fast)
#define AUDIO_DEBUG_COM_TX_MESSAGES_QUEUE_SIZE      (10)  // Hold incoming messages from peripheral till uart is send data. So, should be not big(sending operation is fast)

#define AUDIO_DEBUG_COM_TX_END_SIGNAL               (1<<0)

#define AUDIO_DEBUG_IDLE_TIMEOUT_MS                 (15)

#define AUDIO_DEBUG_LOG_MUX_LOCK()                  (osMutexWait(audio_debug_com.log_mux, osWaitForever)==osOK)
#define AUDIO_DEBUG_LOG_MUX_UNLOCK()                osMutexRelease(audio_debug_com.log_mux)

/****************************************************************************
 * Private Types
 ****************************************************************************/
typedef union {
	uint8_t all;
	struct {
		uint8_t log_gps :1;
		uint8_t log_can :1;
		uint8_t log_keyboard :1;
		uint8_t log_tilt :1;
	} fields;
} audio_debug_log_discrete_t;

typedef struct {
	size_t size;
	uint8_t buff[AUDIO_DEBUG_UART_RX_BUFF_SIZE + 1];
} audio_debug_com_rx_mail_t; // Incoming mail from CAN

typedef struct {
	osThreadId rx_task_handle;       // rx task handle(receive from audio_debug)
	osMailQId rx_queue_mail_handle; // rx mails from irq(received from audio_debug)

	osThreadId tx_task_handle;            // tx task handle(send to audio_debug)
	osMessageQId tx_queue_message_handle; // tx messages queue(send to audio_debug)

	size_t message_buff_size;                          // message buffer
	uint8_t message_buff[AUDIO_DEBUG_UART_RX_BUFF_SIZE + 1]; // message buffer size

	uint8_t idle_timeout_ms;   // idle timeout for audio_debug communication

	audio_debug_log_discrete_t log_discrete;     // flags related to logs
	osMutexId log_mux;          // logs mutex

	bool volatile ignition_on_note_pend;
	uint32_t volatile ignition_on_note_timeout_ms;

} audio_debug_com_t;

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static char audio_debug_half_byte_to_ascII_hex(uint8_t data);
static bool audio_debug_bin_to_ascII(uint8_t *src_p, size_t src_size,
		char *dest_p, size_t dest_size);

static void audio_debug_com_ack_send(const char *ack_p);

// RX
static void audio_debug_com_rx_cpl_cb(uint8_t *data_p, size_t data_len);
static void audio_debug_com_rx_thread(void const *argument);

// TX
static void audio_debug_com_tx_cpl_cb(void);
static void audio_debug_com_tx_thread(void const *argument);

/****************************************************************************
 * Private Data
 ****************************************************************************/
static audio_debug_com_t audio_debug_com = { .ignition_on_note_pend = false };

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/
static char audio_debug_half_byte_to_ascII_hex(uint8_t data) {
	if (data >= 10) {
		return (data - 10) + 0x41;
	}
	return data + 0x30;
}

static bool audio_debug_bin_to_ascII(uint8_t *src_p, size_t src_size,
		char *dest_p, size_t dest_size) {
	if ((src_p == 0) || (src_size == 0) || (dest_p == 0)
			|| (dest_size < (src_size * 3 + 1))) {
		return false;
	}

	size_t cntr = 0;
	for (size_t i = 0; i < src_size; i++) {
		dest_p[cntr++] = audio_debug_half_byte_to_ascII_hex(
				(src_p[i] >> 4) & 0x0F);
		dest_p[cntr++] = audio_debug_half_byte_to_ascII_hex(src_p[i] & 0x0F);
		dest_p[cntr++] = ' ';
	}

	return true;
}

static void audio_debug_com_ack_send(const char *ack_p) {
	if ((ack_p == NULL) || (strlen(ack_p) == 0)) {
		return;
	}
	// send ACK
	queue_message_put(audio_debug_com.tx_queue_message_handle,
	NULL, 0, (void*) ack_p, strlen(ack_p),
	NULL, 0, 0);
}

// RX
static void audio_debug_com_rx_cpl_cb(uint8_t *data_p, size_t data_len) {
	if ((data_p == NULL) || (data_len == 0)) {
		return;
	}
	if (data_len > AUDIO_DEBUG_UART_RX_BUFF_SIZE) {
		data_len = AUDIO_DEBUG_UART_RX_BUFF_SIZE;
	}
	// refresh idle timeout
	audio_debug_com.idle_timeout_ms = AUDIO_DEBUG_IDLE_TIMEOUT_MS;

	queue_mail_put(audio_debug_com.rx_queue_mail_handle, data_p, data_len, 0);

	// copy new data to message buffer
}

static void audio_debug_com_rx_thread(void const *argument) {
	audio_debug_com_rx_mail_t *mail_p = NULL;

	while (1) {
		// get mail from can rx mail queue
		mail_p = (audio_debug_com_rx_mail_t*) queue_mail_get(
				audio_debug_com.rx_queue_mail_handle, osWaitForever);
		if (mail_p != NULL) {

			int count = 0;
			for (count = 0; count < mail_p->size; ++count) {
				if (mail_p->buff[count] == '*') {
					count++;
					break;
				}
			}

			// process incoming commands(it is the same handler as for GPS because same commands coming to GPS and to here)
			gps_com_cmd_processing(mail_p->buff, count,
			PERIPHERAL_UART_AUDIO_DEBUG_NUM, audio_debug_com_ack_send);

			// free mail
			queue_mail_free(audio_debug_com.rx_queue_mail_handle, mail_p);
			mail_p = NULL;
		}

		watchdog_reset();
	}
}

// TX
static void audio_debug_com_tx_cpl_cb(void) {
	osSignalSet(audio_debug_com.tx_task_handle, AUDIO_DEBUG_COM_TX_END_SIGNAL);
}

static void ignition_on_note_task() {

	if (!audio_debug_com.ignition_on_note_pend)
		return;

	if (audio_debug_com.ignition_on_note_timeout_ms
			< IGNITION_ON_NOTE_TIMEOUT_MS)
		return;

	audio_debug_com_write('01');

	audio_debug_com.ignition_on_note_pend = false;

}

static void audio_debug_com_tx_thread(void const *argument) {
	bool tx_status = false;
	queue_message_t *message_p = NULL;

	while (1) {

		// get message from can tx message queue
//    message_p = queue_message_get(audio_debug_com.tx_queue_message_handle, osWaitForever);
		message_p = queue_message_get(audio_debug_com.tx_queue_message_handle,
				osWaitForever);

		/*if (!message_p) {
		 ignition_on_note_task();
		 watchdog_reset();
		 continue;
		 }*/

		if (message_p != NULL) {

			if ((message_p->buff[0] == 'T' && message_p->buff[1] == 'O')
					|| (message_p->buff[0] == 'C' && message_p->buff[1] == 'M')
					|| audio_debug_com.log_discrete.fields.log_can == 1
					|| audio_debug_com.log_discrete.fields.log_gps == 1
					|| audio_debug_com.log_discrete.fields.log_tilt == 1
					|| audio_debug_com.log_discrete.fields.log_keyboard == 1) {
				tx_status = audio_debug_uart_send(message_p->buff,
						message_p->size, audio_debug_com_tx_cpl_cb);

				// wait end of tx
				if (tx_status) {
					osSignalWait(AUDIO_DEBUG_COM_TX_END_SIGNAL, 1000);
				}

				// free message
				queue_message_free(message_p);
				message_p = NULL;
			} else {
				HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_SET);
				osDelay(2000);

				// clr tx end signal
				osSignalWait(AUDIO_DEBUG_COM_TX_END_SIGNAL, 0);

				// send message
				tx_status = audio_debug_uart_send(message_p->buff,
						message_p->size, audio_debug_com_tx_cpl_cb);

				// wait end of tx
				if (tx_status) {
					osSignalWait(AUDIO_DEBUG_COM_TX_END_SIGNAL, 1000);
				}

				// free message
				queue_message_free(message_p);
				message_p = NULL;

				osDelay(5000);
				HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_RESET);
			}
		}
		watchdog_reset();
	}
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
bool audio_debug_com_init(void) {
	// mutex for logs
	osMutexDef(log_mux);
	audio_debug_com.log_mux = osMutexCreate(osMutex(log_mux));
	if (audio_debug_com.log_mux == NULL) {
		Error_Handler();
		return false;
	}

	// RX
	// create rx mail queue
	osMailQDef(rx_mail_queue, AUDIO_DEBUG_COM_RX_MAILS_QUEUE_SIZE,
			audio_debug_com_rx_mail_t);
	audio_debug_com.rx_queue_mail_handle = osMailCreate(osMailQ(rx_mail_queue),
	NULL);
	if (audio_debug_com.rx_queue_mail_handle == NULL) {
		Error_Handler();
		return false;
	}

	// create rx task
	osThreadDef(audio_debug_rx_task, audio_debug_com_rx_thread,
			osPriorityNormal, 0, configMINIMAL_STACK_SIZE * 30);
	audio_debug_com.rx_task_handle = osThreadCreate(
			osThread(audio_debug_rx_task), NULL);
	if (audio_debug_com.rx_task_handle == NULL) {
		Error_Handler();
		return false;
	}

	// TX
	// create tx message queue
	uint32_t tmp = 0;
	osMessageQDef(tx_message_queue, AUDIO_DEBUG_COM_TX_MESSAGES_QUEUE_SIZE,
			&tmp);
	audio_debug_com.tx_queue_message_handle = osMessageCreate(
			osMessageQ(tx_message_queue), NULL);
	if (audio_debug_com.tx_queue_message_handle == NULL) {
		Error_Handler();
		return false;
	}

	// create tx task
	osThreadDef(audio_debug_tx_task, audio_debug_com_tx_thread,
			osPriorityNormal, 0, configMINIMAL_STACK_SIZE * 4);
	audio_debug_com.tx_task_handle = osThreadCreate(
			osThread(audio_debug_tx_task), NULL);
	if (audio_debug_com.tx_task_handle == NULL) {
		Error_Handler();
		return false;
	}

	// uart init
	if (!audio_debug_uart_init(audio_debug_com_rx_cpl_cb, false)) {
		Error_Handler();
		return false;
	}

	return true;
}

void audio_debug_com_schedule_ignition_on_note() {
	audio_debug_com.ignition_on_note_timeout_ms = 0;
	audio_debug_com.ignition_on_note_pend = true;
}

void audio_debug_com_time_proc() {

	if (audio_debug_com.idle_timeout_ms > 0) {
		if (audio_debug_com.idle_timeout_ms > 1) {
			audio_debug_com.idle_timeout_ms--;
		} else {
			if (audio_debug_uart_is_rx_started()) {
				// rx ongoing - refresh timeout
				audio_debug_com.idle_timeout_ms = AUDIO_DEBUG_IDLE_TIMEOUT_MS;
			} else {
				// idle
				// send all data to mail
				if (audio_debug_com.message_buff_size > 0) {
					queue_mail_put(audio_debug_com.rx_queue_mail_handle,
							audio_debug_com.message_buff,
							audio_debug_com.message_buff_size, 0);
					memset(audio_debug_com.message_buff, 0,
							sizeof(audio_debug_com.message_buff));
					audio_debug_com.message_buff_size = 0;
				}
				audio_debug_com.idle_timeout_ms = 0;
			}
		}
	}

	if (audio_debug_com.ignition_on_note_timeout_ms
			< IGNITION_ON_NOTE_TIMEOUT_MS)
		++audio_debug_com.ignition_on_note_timeout_ms;

}

bool audio_debug_com_is_idle(void) {
	if (audio_debug_com.idle_timeout_ms == 0) {
		return true;
	}
	return false;
}

osMessageQId audio_debug_com_get_tx_messages_queue(void) {
	return audio_debug_com.tx_queue_message_handle;
}

void audio_debug_com_execute_log_gps_cmd(uint8_t *data_p, size_t data_len,
		uint8_t src_type, void (*ack_send_func_p)(const char *ack_p)) {
	if (AUDIO_DEBUG_LOG_MUX_LOCK()) {

		audio_debug_com.log_discrete.all = 0;
		audio_debug_com.log_discrete.fields.log_gps = 1;

		AUDIO_DEBUG_LOG_MUX_UNLOCK();
		audio_debug_com_ack_send("CMD,OK");

		if (!audio_debug_uart_init(audio_debug_com_rx_cpl_cb, true)) {
			Error_Handler();
			return false;
		}
	}
}

void audio_debug_com_execute_log_can_cmd(uint8_t *data_p, size_t data_len,
		uint8_t src_type, void (*ack_send_func_p)(const char *ack_p)) {
	if (AUDIO_DEBUG_LOG_MUX_LOCK()) {

		audio_debug_com.log_discrete.all = 0;
		audio_debug_com.log_discrete.fields.log_can = 1;

		AUDIO_DEBUG_LOG_MUX_UNLOCK();
		audio_debug_com_ack_send("CMD,OK");

		if (!audio_debug_uart_init(audio_debug_com_rx_cpl_cb, true)) {
			Error_Handler();
			return false;
		}
	}
}

void audio_debug_com_execute_log_fm_cmd(uint8_t *data_p, size_t data_len,
		uint8_t src_type, void (*ack_send_func_p)(const char *ack_p)) {
	if (AUDIO_DEBUG_LOG_MUX_LOCK()) {

		audio_debug_com.log_discrete.all = 0;
		audio_debug_com.log_discrete.fields.log_keyboard = 1;

		AUDIO_DEBUG_LOG_MUX_UNLOCK();
		audio_debug_com_ack_send("CMD,OK");

		if (!audio_debug_uart_init(audio_debug_com_rx_cpl_cb, true)) {
			Error_Handler();
			return false;
		}
	}
}

void audio_debug_com_execute_log_tilt_cmd(uint8_t *data_p, size_t data_len,
		uint8_t src_type, void (*ack_send_func_p)(const char *ack_p)) {
	if (AUDIO_DEBUG_LOG_MUX_LOCK()) {

		audio_debug_com.log_discrete.all = 0;
		audio_debug_com.log_discrete.fields.log_tilt = 1;

		AUDIO_DEBUG_LOG_MUX_UNLOCK();
		audio_debug_com_ack_send("CMD,OK");

		if (!audio_debug_uart_init(audio_debug_com_rx_cpl_cb, true)) {
			Error_Handler();
			return false;
		}
	}
}

void audio_debug_com_execute_log_off_cmd(uint8_t *data_p, size_t data_len,
		uint8_t src_type, void (*ack_send_func_p)(const char *ack_p)) {
	audio_debug_com_clr_log();

	audio_debug_com.log_discrete.all = 0;
	audio_debug_com.log_discrete.fields.log_can = 0;
	audio_debug_com.log_discrete.fields.log_gps = 0;
	audio_debug_com.log_discrete.fields.log_tilt = 0;
	audio_debug_com.log_discrete.fields.log_keyboard = 0;

	queue_message_clr(audio_debug_com.tx_queue_message_handle);

	audio_debug_com_ack_send("CMD,OK");

	if (!audio_debug_uart_init(audio_debug_com_rx_cpl_cb, false)) {
		Error_Handler();
		return false;
	}
}

void audio_debug_com_clr_log(void) {
	if (AUDIO_DEBUG_LOG_MUX_LOCK()) {

		audio_debug_com.log_discrete.all = 0;

		AUDIO_DEBUG_LOG_MUX_UNLOCK();
	}
}

void audio_debug_com_write(uint8_t *data_p) {
	if (!AUDIO_DEBUG_LOG_MUX_LOCK()) {
		return;
	}

	uint8_t message_1[] = ("\x7E\xFF\x06\x0F\x00");
	uint8_t message_2[] = ("\x01\xEF");
	uint8_t message_3[1];
	strncpy(message_3, data_p, 1);

	uint8_t result[8];

	strcpy(result, message_1);

	memset(result + 4, 0, 1);
	memset(result + 5, (int) strtol(data_p, NULL, 16), 1);
	strcpy(result + 6, message_2);

	queue_message_put(audio_debug_com.tx_queue_message_handle,
	NULL, 0, result, 8,
	NULL, 0, 0);

	AUDIO_DEBUG_LOG_MUX_UNLOCK();

}

void audio_debug_com_send_log(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, bool is_hex) {
	if ((data_p == NULL) || (data_len == 0)) {
		return;
	}

	char *asc_p = NULL;

	// lock
	if (!AUDIO_DEBUG_LOG_MUX_LOCK()) {
		return;
	}

	// check if logs enabled on this uart
	if ((uart_num == PERIPHERAL_UART_GPS_NUM
			&& audio_debug_com.log_discrete.fields.log_gps)
			|| (uart_num == PERIPHERAL_UART_CAN_NUM
					&& audio_debug_com.log_discrete.fields.log_can)
			|| (uart_num == PERIPHERAL_UART_KEYBOARD_NUM
					&& audio_debug_com.log_discrete.fields.log_keyboard)
			|| (uart_num == PERIPHERAL_UART_TILT_SENSOR_NUM
					&& audio_debug_com.log_discrete.fields.log_tilt)) {

		// check is incomming data in hex(binary) format
		if (is_hex) {
			// hex(binary) format

			// alloc buff for conversion
			uint16_t asc_len = (data_len * 3) + 1;
			asc_p = (char*) pvPortMalloc(asc_len);
			if (asc_p == NULL) {
				// failed to allocate memory
				goto END;
			}
			memset(asc_p, 0, asc_len);

			// convert to ascII
			if (!audio_debug_bin_to_ascII(data_p, data_len, asc_p, asc_len)) {
				// failed to convert to ascII
				goto END;
			}

			data_p = (uint8_t*) asc_p;
			data_len = strlen(asc_p);
		} else {

			// send data
			queue_message_put(audio_debug_com.tx_queue_message_handle,
			NULL, 0, (void*) data_p, data_len,
			NULL, 0, 0);
		}
	}

	END:
	// free asc buff
	if (asc_p != NULL) {
		vPortFree(asc_p);
	}

	// unlock
	AUDIO_DEBUG_LOG_MUX_UNLOCK();
}

