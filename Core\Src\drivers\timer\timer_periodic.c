/****************************************************************************
 *   Copyright (C) 2020.01.03 All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "timer_periodic.h"
#include "../../gps_com/gps_com.h"
#include "../../can_com/can_com.h"
#include "../../audio_debug_com/audio_debug_com.h"
#include "../../tilt_sensor_com/tilt_sensor_com.h"
#include "../../rpm/rpm.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define PERIODIC_TIMER              TIM7

#define PERIODIC_TIMER_PRESCALLER   (72-1)
#define PERIODIC_TIMER_PERIOD       1000

#define PERIODIC_TIMER_IRQn         TIM7_IRQn
#define PERIODIC_TIMER_PREEM_PRIO   15
#define PERIODIC_TIMER_SUB_PRIO     0

#define PERIODIC_TIMER_CLK_ENABLE   __HAL_RCC_TIM7_CLK_ENABLE
#define PERIODIC_TIMER_CLK_DISABLE  __HAL_RCC_TIM7_CLK_DISABLE

/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Private Data
 ****************************************************************************/
TIM_HandleTypeDef htim_periodic;

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Public Functions
 ****************************************************************************/
TIM_HandleTypeDef* timer_periodic_get(void)
{
  return &htim_periodic;
}

bool timer_periodic_init(void)
{
  htim_periodic.Instance               = PERIODIC_TIMER;
  htim_periodic.Init.Prescaler         = PERIODIC_TIMER_PRESCALLER;
  htim_periodic.Init.CounterMode       = TIM_COUNTERMODE_UP;
  htim_periodic.Init.Period            = PERIODIC_TIMER_PERIOD;
  htim_periodic.Init.ClockDivision     = TIM_CLOCKDIVISION_DIV1;
  htim_periodic.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
  if (HAL_TIM_Base_Init(&htim_periodic) != HAL_OK) {
    // LOG("TIMER_PERIODIC", "Init error!\n\r");
    Error_Handler();
    return false;
  }
  HAL_TIM_Base_Start_IT(&htim_periodic);
  return true;
}

void timer_periodic_msp_init(void)
{
  PERIODIC_TIMER_CLK_ENABLE();
  HAL_NVIC_SetPriority(PERIODIC_TIMER_IRQn, PERIODIC_TIMER_PREEM_PRIO, PERIODIC_TIMER_SUB_PRIO);
  HAL_NVIC_EnableIRQ(PERIODIC_TIMER_IRQn);
}

void timer_periodic_msp_deinit(void)
{
  PERIODIC_TIMER_CLK_DISABLE();
  HAL_NVIC_DisableIRQ(PERIODIC_TIMER_IRQn);
}

void timer_periodic_cb(void)
{
  gps_com_time_proc();
  can_com_time_proc();
  audio_debug_com_time_proc();
  tilt_sensor_com_time_proc();
  rpm_time_proc();
  osDelay(1000);
}

void PERIODIC_TIMER_IRQHandler(void)
{
  HAL_TIM_IRQHandler(&htim_periodic);
}
