#ifndef GEOFENCE_H
#define GEOFENCE_H

#include <stdbool.h>
#include <stdint.h>
#include "../config/config.h"

#define MAX_GEOFENCES 20

extern volatile bool geofence_config_changed;

typedef struct {
    float latitude;
    float longitude;
} Coordinate;

typedef struct {
    char code[5];
    uint8_t in_out;
    uint8_t speed_max;
    Coordinate upper_left;
    Coordinate lower_right;
    bool is_active;
    bool speed_alarm_active;
} RectangularGeofence;

typedef struct {
    char code[5];
    uint8_t in_out;
    uint8_t speed_max;
    Coordinate center;
    float radius;
    bool is_active;
    bool speed_alarm_active;
} CircularGeofence;

extern RectangularGeofence rect_geofences[MAX_GEOFENCES];
extern CircularGeofence circ_geofences[MAX_GEOFENCES];
extern uint8_t rect_geofence_count;
extern uint8_t circ_geofence_count;

void geofence_init(void);
void geofence_process(float latitude, float longitude, uint8_t speed);
bool geofence_check_rectangular(float lat, float lon, const RectangularGeofence* fence);
bool geofence_check_circular(float lat, float lon, const CircularGeofence* fence);
void geofence_trigger_alarm(const char* code, bool is_entry);
void geofence_trigger_speed_alarm(const char* code, uint8_t speed);

#endif // GEOFENCE_H
