/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _AUDIO_DEBUG_UART_H_
#define _AUDIO_DEBUG_UART_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define AUDIO_DEBUG_UART_IRQn               UART4_IRQn
#define AUDIO_DEBUG_UART_PREEM_PRIO         9
#define AUDIO_DEBUG_UART_SUB_PRIO           0

#define AUDIO_DEBUG_UART_DMA_TX_IRQn        DMA2_Channel4_5_IRQn
#define AUDIO_DEBUG_UART_DMA_TX_IRQHandler  DMA2_Channel4_5_IRQHandler
#define AUDIO_DEBUG_UART_DMA_TX_PREEM_PRIO  9
#define AUDIO_DEBUG_UART_DMA_TX_SUB_PRIO    0

#define AUDIO_DEBUG_UART_DMA_RX_IRQn        DMA2_Channel3_IRQn
#define AUDIO_DEBUG_UART_DMA_RX_IRQHandler  DMA2_Channel3_IRQHandler
#define AUDIO_DEBUG_UART_DMA_RX_PREEM_PRIO  9
#define AUDIO_DEBUG_UART_DMA_RX_SUB_PRIO    0

#define AUDIO_DEBUG_UART_RX_BUFF_SIZE       1000
/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
UART_HandleTypeDef* audio_debug_uart_get(void);

bool audio_debug_uart_init(void (*rx_cpl_cb)(uint8_t* data_p, size_t data_len),bool high_speed);
bool audio_debug_uart_reinit();
void audio_debug_uart_msp_init(void);
void audio_debug_uart_msp_deinit(void);

bool audio_debug_uart_is_rx_started(void);
void audio_debug_uart_rx_cpl_cb(void);

bool audio_debug_uart_send(uint8_t* data, size_t data_len, void (*tx_cpl_cb)(void));
void audio_debug_uart_tx_cpl_cb(void);

void audio_debug_uart_err_cb(void);

void AUDIO_DEBUG_UART_DMA_RX_IRQHandler(void);
void AUDIO_DEBUG_UART_DMA_TX_IRQHandler(void);
void AUDIO_DEBUG_UART_IRQHandler(void);

#endif // _AUDIO_DEBUG_UART_H_
