/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "gps_com.h"
#include "../drivers/uart/gps_uart.h"

#include "../out_ports/out_ports.h"
#include "../in_ports/in_ports.h"
#include "../can_com/can_com.h"
#include "../drivers/uart/can_uart.h"

#include "../audio_debug_com/audio_debug_com.h"
#include "../drivers/uart/audio_debug_uart.h"

#include "../tilt_sensor_com/tilt_sensor_com.h"
#include "../drivers/uart/tilt_sensor_uart.h"

#include "../keyboard_com/keyboard_com.h"
#include "../drivers/uart/keyboard_uart.h"

#include "../tpms_com/tpms_com.h"
#include "../drivers/uart/tpms_uart.h"

#include "../queue_mail/queue_mail.h"
#include "../queue_message/queue_message.h"

#include "../config/config.h"
#include "../rpm/rpm.h"
#include "../watchdog/watchdog.h"
#include "../geofence/geofence.h"
#include "../speed_control/speed_control.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define GPS_COM_RX_MAILS_QUEUE_SIZE     (15) // Hold incoming mails from GPS till RX task processing it. So, should be not big(processing is fast)
#define GPS_COM_TX_MESSAGES_QUEUE_SIZE  (15) // Holds data that should be sent back to GPS

#define GPS_COM_TX_END_SIGNAL           (1<<0)

#define GPS_IDLE_TIMEOUT_MS             (15)

// timeouts for speed(from can message) refresh(to fm device)
#define GPS_START_UP_SPEED_REFRESH_TIMEOUT_MS     (3*1000)
#define GPS_SPEED_REFRESH_TIMEOUT_MS              (10*1000)

#define GPS_SPEED_REFRESH_MESSAGE_SPEED_INDEX     (3)
#define GPS_SPEED_REFRESH_MESSAGE_SUM_INDEX       (9)

#define GPS_UART_RX_BUFF_SIZE       700

#define NMEA_END_CHAR_1 '\n'
#define NMEA_MAX_LENGTH 100
/****************************************************************************
 * Private Types
 ****************************************************************************/
typedef struct {
	size_t size;
	uint8_t buff[GPS_UART_RX_BUFF_SIZE + 1];
} gps_com_rx_mail_t; // Incoming mail from GPS

typedef struct {
	osThreadId rx_task_handle;           // rx task handle(receive from GPS)
	osMailQId rx_queue_mail_handle;     // rx mails from irq(received from GPS)

	osMessageQId tx_queue_message_handle; // messages that should be sent back to GPS

	uint8_t tx_uart_num;              // current tx uart number
	osThreadId tx_task_handle;           // tx task handle(send to GPS)

	size_t message_buff_size;                          // message buffer
	uint8_t message_buff[GPS_UART_RX_BUFF_SIZE + 1];      // message buffer size

	uint16_t idle_timeout_ms;

	uint32_t speed_refresh_timeout_ms; // send speed(from can message) to fm device

	bool is_sw_watchdog_cmd_received; // flag that software watchdog command received from GPS
	uint16_t hub_ready_timeout_ms;                // timeout for 'hub,ready' cmd
	uint32_t sw_watchdog_timeout_ms;                // software watchdog counter

	char code_geo_1_activated_rect_in[4];
	char code_geo_2_activated_rect_in[4];
	char code_geo_3_activated_rect_in[4];
	char code_geo_4_activated_rect_in[4];
	char code_geo_5_activated_rect_in[4];

	char code_geo_1_activated_rect_out[4];
	char code_geo_2_activated_rect_out[4];
	char code_geo_3_activated_rect_out[4];
	char code_geo_4_activated_rect_out[4];
	char code_geo_5_activated_rect_out[4];

	char code_geo_1_activated_circ_in[4];
	char code_geo_2_activated_circ_in[4];
	char code_geo_3_activated_circ_in[4];
	char code_geo_4_activated_circ_in[4];
	char code_geo_5_activated_circ_in[4];

	char code_geo_1_activated_circ_out[4];
	char code_geo_2_activated_circ_out[4];
	char code_geo_3_activated_circ_out[4];
	char code_geo_4_activated_circ_out[4];
	char code_geo_5_activated_circ_out[4];

	int count_vel_1;
	bool alarm_vel_1;

	int count_vel_2;
	bool alarm_vel_2;

	bool beep_on_vel_1;
	bool beep_on_vel_2;

	bool speed_geo_1_alarm_rect_in;
	bool speed_geo_2_alarm_rect_in;
	bool speed_geo_3_alarm_rect_in;
	bool speed_geo_4_alarm_rect_in;
	bool speed_geo_5_alarm_rect_in;

	bool speed_geo_1_alarm_rect_out;
	bool speed_geo_2_alarm_rect_out;
	bool speed_geo_3_alarm_rect_out;
	bool speed_geo_4_alarm_rect_out;
	bool speed_geo_5_alarm_rect_out;

	bool speed_geo_1_alarm_circ_in;
	bool speed_geo_2_alarm_circ_in;
	bool speed_geo_3_alarm_circ_in;
	bool speed_geo_4_alarm_circ_in;
	bool speed_geo_5_alarm_circ_in;

	bool speed_geo_1_alarm_circ_out;
	bool speed_geo_2_alarm_circ_out;
	bool speed_geo_3_alarm_circ_out;
	bool speed_geo_4_alarm_circ_out;
	bool speed_geo_5_alarm_circ_out;

	int speed_gps;
	float hdop;

    int unidentified_driver_count;
    bool notification_enabled;
} gps_com_t;

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static uint8_t hex_to_half_byte(char hex_symbol);
static bool hex_str_to_byte(char *hex_str_p, size_t hex_str_len,
		uint8_t *buff_p, size_t buff_len);

// RX
void gps_com_rx_cpl_cb(uint8_t *data_p, size_t data_len);
osMessageQId gps_com_get_peripheral_uart_tx_queue(uint8_t uart_num);

static void gps_com_execute_gpio_set_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_data_write_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_uart_configure_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_rpm_configure_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_rpm_clr_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_set_speed_conversion_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_rollover_set_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_pulse_set_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_odo_clr_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_watchdog_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_reboot_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_config_get_configure_cmd(uint8_t *data_p,
        size_t data_len, uint8_t uart_num,
        void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_config_set_cmd(uint8_t *data_p, size_t data_len,
        uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_execute_set_engine_cut_cmd(uint8_t *data_p, size_t data_len,
        uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p));
static void gps_com_ack_send(const char *ack_p);

static void gps_sw_watchdog_proc(void);

static void gps_com_rx_thread(void const *argument);
static int normalize_sms_message(char *str, int len);

// TX
static void gps_com_tx_cpl_cb(void);
static void gps_com_tx_thread(void const *argument);

static void gps_speed_refresh(void);
static void gps_sw_watchdog_proc(void);

/****************************************************************************
 * Private Data
 ****************************************************************************/
static gps_com_t gps_com = { .tx_uart_num = PERIPHERAL_UART_GPS_NUM,
		.speed_refresh_timeout_ms = GPS_START_UP_SPEED_REFRESH_TIMEOUT_MS };
/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

float degreesToRadians(float degrees) {
	return degrees * (3.14159265358979323846 / 180);
}

float haversineDistance(float lat1, float lon1, float lat2, float lon2) {

	lat1 = degreesToRadians(lat1);
	lon1 = degreesToRadians(lon1);
	lat2 = degreesToRadians(lat2);
	lon2 = degreesToRadians(lon2);

	float dLat = lat2 - lat1;
	float dLon = lon2 - lon1;

	float a = sin(dLat / 2) * sin(dLat / 2)
			+ cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
	float c = 6371000 * 2 * atan2(sqrt(a), sqrt(1 - a));

	return c;
}

static uint8_t hex_to_half_byte(char hex_symbol) {
	if ((hex_symbol >= '0') && (hex_symbol <= '9')) {
		// number
		return ((hex_symbol - '0') & 0x0f);
	} else if ((hex_symbol >= 'A') && (hex_symbol <= 'F')) {
		// A - F
		return (((hex_symbol - 'A') + 10) & 0x0f);
	} else if ((hex_symbol >= 'a') && (hex_symbol <= 'f')) {
		// a- f
		return (((hex_symbol - 'a') + 10) & 0x0f);
	} else {
		// not hex
		return 0;
	}
}

static bool hex_str_to_byte(char *hex_str_p, size_t hex_str_len,
		uint8_t *buff_p, size_t buff_len) {
	if ((hex_str_p == NULL) || (hex_str_len == 0) || (buff_p == NULL)
			|| (buff_len == 0) || (buff_len < (hex_str_len / 2))) {
		return false;
	}
	for (size_t i = 0; i < (hex_str_len / 2); i++) {
		buff_p[i] = hex_to_half_byte(hex_str_p[2 * i]) << 4;
		buff_p[i] |= hex_to_half_byte(hex_str_p[(2 * i) + 1]);
	}
	return true;
}

// RX
void gps_com_rx_cpl_cb(uint8_t *data_p, size_t data_len) {
	if ((data_p == NULL) || (data_len == 0)) {
		return;
	}
	if (data_len > GPS_UART_RX_BUFF_SIZE) {
		data_len = GPS_UART_RX_BUFF_SIZE;
	}
	// refresh idle timeout
	gps_com.idle_timeout_ms = GPS_IDLE_TIMEOUT_MS;

	// put message
	//if ((data_len + gps_com.message_buff_size) > GPS_UART_RX_BUFF_SIZE) {
	// not enough space for new data - send prev data to mail
	//if (gps_com.message_buff_size > 0) {

	memset(gps_com.message_buff, 0, sizeof(gps_com.message_buff));
	memcpy(gps_com.message_buff, data_p, data_len);
	gps_com.message_buff_size = data_len;

	queue_mail_put(gps_com.rx_queue_mail_handle, gps_com.message_buff,
			gps_com.message_buff_size, 0);
	//	}
	// copy new data to message buffer
	//	} else {
	//		// copy new data to message buffer
	//		memcpy(&gps_com.message_buff[gps_com.message_buff_size], data_p,
	//				data_len);
	//		gps_com.message_buff_size += data_len;
	//	}
}

osMessageQId gps_com_get_peripheral_uart_tx_queue(uint8_t uart_num) {
	if ((uart_num < PERIPHERAL_UART_MIN) || (uart_num > PERIPHERAL_UART_MAX)) {
		// uart number is not correct
		return NULL;
	}

	if (uart_num == PERIPHERAL_UART_CAN_NUM) {
		return (osMessageQId) can_com_get_tx_messages_queue();
	} else if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		return (osMessageQId) audio_debug_com_get_tx_messages_queue();
	} else if (uart_num == PERIPHERAL_UART_TILT_SENSOR_NUM) {
		return (osMessageQId) tilt_sensor_com_get_tx_messages_queue();
	} else if (uart_num == PERIPHERAL_UART_TPMS_NUM) {
		//return (osMessageQId) tpms_com_get_tx_messages_queue();
	}

	return NULL;
}

static void gps_com_execute_data_write_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len <= (PERIPHERAL_CMD_START_LEN + PERIPHERAL_CMD_END_LEN)) {
		// not correct message or not ready
		return;
	}
	// check uart number
	uint8_t uart_num_loc = data_p[PERIPHERAL_CMD_WRITE_DATA_UART_NUM_OFFS];
	if ((uart_num_loc < 0x30) || (uart_num_loc > 0x39)) {
		// not number
		return;
	}

	ack_send_func_p("CMD,OK");

	uart_num_loc -= 0x30;
	// get peripherel uart tx queue
	osMessageQId peripheral_tx_message_queue =
			gps_com_get_peripheral_uart_tx_queue(uart_num_loc);
	if (peripheral_tx_message_queue == NULL) {
		// not correct peripheral uart number
		return;
	}

	// gps send data in ascII. Convert it to bytes if data going to FM or PERIPH4 or TILT(PERIPH5)
	uint8_t *payload_start_p = &data_p[PERIPHERAL_CMD_WRITE_DATA_DATA_OFFS];
	size_t payload_size = data_len
			- (PERIPHERAL_CMD_START_LEN + PERIPHERAL_CMD_END_LEN);
	if ((uart_num_loc == PERIPHERAL_UART_AUDIO_DEBUG_NUM)
			|| (uart_num_loc == PERIPHERAL_UART_TILT_SENSOR_NUM)) {
		uint8_t *tmp_p = pvPortMalloc(payload_size);
		if (tmp_p == NULL) {
			// mem allocation error!
			return;
		}
		// Remove spaces from incomming payload
		size_t payload_size_new = 0;
		for (size_t i = 0; i < payload_size; i++) {
			if (payload_start_p[i] != ' ') {
				tmp_p[payload_size_new] = payload_start_p[i];
				payload_size_new++;
			}
		}
		// conversion to byte
		if (!hex_str_to_byte((char*) tmp_p, payload_size_new, payload_start_p,
				(payload_size_new / 2))) {
			// conversion error
			vPortFree(tmp_p);
			return;
		}
		payload_size = payload_size_new / 2;
		vPortFree(tmp_p);
	}

	// send data to peripheral tx queue
	queue_message_put(peripheral_tx_message_queue,
	NULL, 0, payload_start_p, payload_size,
	NULL, 0, 0);

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_memory_clear_configure_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_CLEAR_MERMORY_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->loaded = false;

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}

	//Reset system
	NVIC_SystemReset();
}

static void gps_com_execute_rpm_def_configure_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_RPM_DEF_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_RPM_DEF_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);
		int index = 0;

		while (split_comma != NULL) {
			if (index == 0)
				config_file->rpm_green_count = atoi(split_comma);
			else if (index == 1)
				config_file->rpm_yellow_count = atoi(split_comma);
			else if (index == 2)
				config_file->rpm_red_count = atoi(split_comma);
			else if (index == 3)
				config_file->downhill_count = atoi(split_comma);

			split_comma = strtok_r(NULL, delim, &end_buff);
			index++;
		}

		save_config();
	}

	config_mutex_give();

	can_com_set_counters(config_file->rpm_green_count,
			config_file->rpm_yellow_count, config_file->rpm_red_count,
			config_file->downhill_count);

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_turbo_def_configure_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_TURBO_DEF_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_TURBO_DEF_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->turbo_count = atoi(buff);
		can_com_set_turbo_counter(config_file->turbo_count);

		save_config();
	}

	config_mutex_give();

	can_com_set_counters(config_file->rpm_green_count,
			config_file->rpm_yellow_count, config_file->rpm_red_count,
			config_file->downhill_count);

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_rpm_configure_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_RPM_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_RPM_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);
		int index = 0;

		while (split_comma != NULL) {
			if (index == 0)
				config_file->rpm_green_min = atoi(split_comma);
			else if (index == 1)
				config_file->rpm_green_max = atoi(split_comma);
			else if (index == 2)
				config_file->rpm_yellow_min = atoi(split_comma);
			else if (index == 3)
				config_file->rpm_yellow_max = atoi(split_comma);
			else if (index == 4)
				config_file->rpm_red_min = atoi(split_comma);
			else if (index == 5)
				config_file->rpm_red_max = atoi(split_comma);
			else if (index == 6)
				config_file->downhill_rpm_max = atoi(split_comma);
			else if (index == 7)
				config_file->downhill_speed_min = atoi(split_comma);
			else if (index == 8)
				config_file->downhill_seconds_min = atoi(split_comma);

			split_comma = strtok_r(NULL, delim, &end_buff);
			index++;
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_keyboard_configure_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_KEYBOARD_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[20];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_KEYBOARD_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->keyboard_type = atoi(buff);

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_uart_configure_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_UART_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_UART_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);
		int index = 0;

		int uart_selected = 2;

		while (split_comma != NULL) {
			if (index == 0)
				uart_selected = atoi(split_comma);
			else if (index == 1) {
				if (uart_selected == 2)
					config_file->uart2_speed = atoi(split_comma);
				else if (uart_selected == 3)
					config_file->uart3_speed = atoi(split_comma);
				else if (uart_selected == 4)
					config_file->uart4_speed = atoi(split_comma);
				else if (uart_selected == 5)
					config_file->uart5_speed = atoi(split_comma);
				else if (uart_selected == 6)
					config_file->uart6_speed = atoi(split_comma);
			} else if (index == 2) {
				if (uart_selected == 2)
					config_file->uart2_bits = atoi(split_comma);
				else if (uart_selected == 3)
					config_file->uart3_bits = atoi(split_comma);
				else if (uart_selected == 4)
					config_file->uart4_bits = atoi(split_comma);
				else if (uart_selected == 5)
					config_file->uart5_bits = atoi(split_comma);
				else if (uart_selected == 6)
					config_file->uart6_bits = atoi(split_comma);
			} else if (index == 3) {
				if (uart_selected == 2)
					config_file->uart2_stop_bit = atoi(split_comma);
				else if (uart_selected == 3)
					config_file->uart3_stop_bit = atoi(split_comma);
				else if (uart_selected == 4)
					config_file->uart4_stop_bit = atoi(split_comma);
				else if (uart_selected == 5)
					config_file->uart5_stop_bit = atoi(split_comma);
				else if (uart_selected == 6)
					config_file->uart6_stop_bit = atoi(split_comma);
			} else if (index == 4) {
				if (uart_selected == 2)
					strncpy(config_file->uart2_flow_control, split_comma, 1);
				else if (uart_selected == 3)
					strncpy(config_file->uart3_flow_control, split_comma, 1);
				else if (uart_selected == 4)
					strncpy(config_file->uart4_flow_control, split_comma, 1);
				else if (uart_selected == 5)
					strncpy(config_file->uart5_flow_control, split_comma, 1);
				else if (uart_selected == 6)
					strncpy(config_file->uart6_flow_control, split_comma, 1);
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
			index++;
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_out_configure_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_OUT_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_OUT_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);
		int index = 0;

		int uart_selected = 3;

		while (split_comma != NULL) {
			if (index == 0)
				uart_selected = atoi(split_comma);
			else if (index == 1) {
				if (uart_selected == 2) {
					config_file->out2_enabled = atoi(split_comma);

					uint8_t alarme[10];
					memset(alarme, 0, sizeof alarme);
					strncpy(alarme, "ALM,142,", 8);
					if (config_file->out2_enabled == 1)
						strncat(alarme, "1", 1);
					else
						strncat(alarme, "0", 1);

					strncat(alarme, "*", 1);
					queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
							alarme, strlen(alarme), NULL, 0, 0);

				} else if (uart_selected == 3) {
					config_file->out3_enabled = atoi(split_comma);

					uint8_t alarme[10];
					memset(alarme, 0, sizeof alarme);
					strncpy(alarme, "ALM,143,", 8);
					if (config_file->out3_enabled == 1)
						strncat(alarme, "1", 1);
					else
						strncat(alarme, "0", 1);

					strncat(alarme, "*", 1);
					queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
							alarme, strlen(alarme), NULL, 0, 0);
				} else if (uart_selected == 4) {
					config_file->out4_enabled = atoi(split_comma);

					uint8_t alarme[10];
					memset(alarme, 0, sizeof alarme);
					strncpy(alarme, "ALM,144,", 8);
					if (config_file->out4_enabled == 1)
						strncat(alarme, "1", 1);
					else
						strncat(alarme, "0", 1);

					strncat(alarme, "*", 1);
					queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
							alarme, strlen(alarme), NULL, 0, 0);
				} else if (uart_selected == 5) {
					config_file->out5_enabled = atoi(split_comma);

					uint8_t alarme[10];
					memset(alarme, 0, sizeof alarme);
					strncpy(alarme, "ALM,145,", 8);
					if (config_file->out5_enabled == 1)
						strncat(alarme, "1", 1);
					else
						strncat(alarme, "0", 1);

					strncat(alarme, "*", 1);
					queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
							alarme, strlen(alarme), NULL, 0, 0);
				}
			} else if (index == 2) {
				if (uart_selected == 2)
					config_file->out2_time = atoi(split_comma);
				else if (uart_selected == 3)
					config_file->out3_time = atoi(split_comma);
				else if (uart_selected == 4)
					config_file->out4_time = atoi(split_comma);
				else if (uart_selected == 5)
					config_file->out5_time = atoi(split_comma);
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
			index++;
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_add_geor_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_GEOR_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_GEOR_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{
		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		int index_item = 0;
		for (int index_comma = 0;
				index_comma < sizeof config_file->array_geofence_rectangular;
				++index_comma) {
			if (strlen(
					config_file->array_geofence_rectangular[index_comma].code)
					== 0
					|| strncmp(
							config_file->array_geofence_rectangular[index_comma].code,
							split_comma, 4) == 0) {
				while (split_comma != NULL) {
					if (index_item == 0) {
						strncpy(
								config_file->array_geofence_rectangular[index_comma].code,
								split_comma, 4);
						index_item++;
					} else if (index_item == 1) {
						config_file->array_geofence_rectangular[index_comma].in_out =
								atoi(split_comma);
						index_item++;
					} else if (index_item == 2) {
						config_file->array_geofence_rectangular[index_comma].speed_max =
								atoi(split_comma);
						index_item++;
					} else if (index_item == 3) {
						config_file->array_geofence_rectangular[index_comma].upper_left_point_lat =
								atof(split_comma);
						index_item++;
					} else if (index_item == 4) {
						config_file->array_geofence_rectangular[index_comma].upper_left_point_long =
								atof(split_comma);
						index_item++;
					} else if (index_item == 5) {
						config_file->array_geofence_rectangular[index_comma].lower_right_point_lat =
								atof(split_comma);
						index_item++;
					} else if (index_item == 6) {
						config_file->array_geofence_rectangular[index_comma].lower_right_point_long =
								atof(split_comma);
						index_item = 0;
						split_comma = strtok_r(NULL, delim, &end_buff);
						break;
					}
					split_comma = strtok_r(NULL, delim, &end_buff);
				}
			}
		}

		save_config();

		geofence_config_changed = true;
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_add_geoc_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_GEOC_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_GEOC_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{
		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		int index_item = 0;
		for (int index_comma = 0;
				index_comma < sizeof config_file->array_geofence_circular;
				++index_comma) {
			if (strlen(config_file->array_geofence_circular[index_comma].code)
					== 0
					|| strncmp(
							config_file->array_geofence_circular[index_comma].code,
							split_comma, 4) == 0) {
				while (split_comma != NULL) {
					if (index_item == 0) {
						strncpy(
								config_file->array_geofence_circular[index_comma].code,
								split_comma, 4);
						index_item++;
					} else if (index_item == 1) {
						config_file->array_geofence_circular[index_comma].in_out =
								atoi(split_comma);
						index_item++;
					} else if (index_item == 2) {
						config_file->array_geofence_circular[index_comma].speed_max =
								atoi(split_comma);
						index_item++;
					} else if (index_item == 3) {
						config_file->array_geofence_circular[index_comma].central_point_lat =
								atof(split_comma);
						index_item++;
					} else if (index_item == 4) {
						config_file->array_geofence_circular[index_comma].central_point_long =
								atof(split_comma);
						index_item++;
					} else if (index_item == 5) {
						config_file->array_geofence_circular[index_comma].radius =
								atof(split_comma);
						index_item = 0;
						split_comma = strtok_r(NULL, delim, &end_buff);
						break;
					}
					split_comma = strtok_r(NULL, delim, &end_buff);
				}
			}
		}

		save_config();

		geofence_config_changed = true;
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_vel_configure_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_VEL_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_VEL_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{
		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);
		int index = 0;

		int uart_selected = 2;

		while (split_comma != NULL) {
			if (index == 0)
				uart_selected = atoi(split_comma);
			else if (index == 1) {
				if (uart_selected == 1)
					config_file->speed_1_speed_beep = atoi(split_comma);
				else if (uart_selected == 2)
					config_file->speed_2_speed_beep = atoi(split_comma);
			} else if (index == 2) {
				if (uart_selected == 1)
					config_file->speed_1_speed_alarms = atoi(split_comma);
				else if (uart_selected == 2)
					config_file->speed_2_speed_alarms = atoi(split_comma);
			} else if (index == 3) {
				if (uart_selected == 1)
					config_file->speed_1_seconds_min = atoi(split_comma);
				else if (uart_selected == 2)
					config_file->speed_2_seconds_min = atoi(split_comma);
			} else if (index == 4) {
				if (uart_selected == 1)
					config_file->speed_1_activate_out2 = atoi(split_comma);
				else if (uart_selected == 2)
					config_file->speed_2_activate_out2 = atoi(split_comma);
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
			index++;
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_mp3_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_MP3_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[2];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_MP3_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len - 1);

	audio_debug_com_write((uint8_t*) buff);

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_sms_cmd(uint8_t *data_p, size_t data_len,
                                    uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
    if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
        // wrong params
        return;
    }
    if (data_len < PERIPHERAL_CMD_SMS_LEN) {
        // not correct message or not ready
        return;
    }

    ack_send_func_p("CMD,OK");

    keyboard_enter_sms_display_mode();

    // Wait for keyboard to be ready (non-blocking)
    while (!keyboard_is_ready_for_sms_display()) {
        osDelay(10); // Short delay to prevent busy-waiting
    }

    config_mutex_take(osWaitForever);
    {
        config_file_t *config_file = config_file_get();
        if (config_file->keyboard_type == 1) {
            // RFID mode; no LCD to update
            config_mutex_give();
            return;
        }
    }
    config_mutex_give();

    uint8_t sendKeyboard[500];

    // get enable/disable
    char *start_p = (char*) &data_p[PERIPHERAL_CMD_SMS_LEN];
    int len = data_len - (start_p - (char*) data_p);
    if (len <= 0) {
        // not correct command
        return;
    }

    // Normalize the SMS message (remove excess spaces, asterisks, and truncate to 32 characters)
    int new_len = normalize_sms_message(start_p, len);
    len = new_len;

    uint8_t message_limpar_primeira_linha[] = ("\x1B\x43\x30\x0D\x0A");
    uint8_t message_limpar_segunda_linha[] = ("\x1B\x43\x31\x0D\x0A");
    uint8_t message_escrever_primeira_linha[] = ("\x1B\x53\x30");
    uint8_t message_escrever_fim_linha[] = ("\x0D\x0A");
    uint8_t message_escrever_segunda_linha[] = ("\x1B\x53\x31");

    queue_message_put(keyboard_com_get_tx_messages_queue(), NULL, 0,
                      message_limpar_primeira_linha,
                      sizeof(message_limpar_primeira_linha) - 1,
                      NULL, 0, 0);

    queue_message_put(keyboard_com_get_tx_messages_queue(), NULL, 0,
                      message_limpar_segunda_linha, sizeof(message_limpar_segunda_linha) - 1,
                      NULL, 0, 0);

    if (len <= 16) {
        uint8_t primeira_linha[25];
        memset(primeira_linha, 0, sizeof primeira_linha);

        int pos = 0;
        memcpy(primeira_linha + pos, message_escrever_primeira_linha, sizeof(message_escrever_primeira_linha) - 1);
        pos += sizeof(message_escrever_primeira_linha) - 1;
        memcpy(primeira_linha + pos, start_p, len);
        pos += len;
        memcpy(primeira_linha + pos, message_escrever_fim_linha, sizeof(message_escrever_fim_linha) - 1);
        pos += sizeof(message_escrever_fim_linha) - 1;

        queue_message_put(keyboard_com_get_tx_messages_queue(), NULL, 0,
                          primeira_linha, pos,
                          NULL, 0, 0);
    } else {
        uint8_t primeira_linha[50];  // Increased size to accommodate two lines
        memset(primeira_linha, 0, sizeof primeira_linha);

        int pos = 0;
        memcpy(primeira_linha + pos, message_escrever_primeira_linha, sizeof(message_escrever_primeira_linha) - 1);
        pos += sizeof(message_escrever_primeira_linha) - 1;
        memcpy(primeira_linha + pos, start_p, 16);
        pos += 16;
        memcpy(primeira_linha + pos, message_escrever_fim_linha, sizeof(message_escrever_fim_linha) - 1);
        pos += sizeof(message_escrever_fim_linha) - 1;

        memcpy(primeira_linha + pos, message_escrever_segunda_linha, sizeof(message_escrever_segunda_linha) - 1);
        pos += sizeof(message_escrever_segunda_linha) - 1;
        memcpy(primeira_linha + pos, start_p + 16, len - 16);
        pos += len - 16;
        memcpy(primeira_linha + pos, message_escrever_fim_linha, sizeof(message_escrever_fim_linha) - 1);
        pos += sizeof(message_escrever_fim_linha) - 1;

        queue_message_put(keyboard_com_get_tx_messages_queue(), NULL, 0,
                          primeira_linha, pos,
                          NULL, 0, 0);
    }

    // send ACK
    if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
        audio_debug_com_clr_log();
        ack_send_func_p("CMD,OK");
    }
}

static int normalize_sms_message(char *str, int len) {
    int read_index = 0;
    int write_index = 0;
    int space_count = 0;
    int char_count = 0;

    while (read_index < len && char_count < 32) {
        if (str[read_index] == ' ') {
            if (space_count == 0 && char_count < 31) {  // Ensure we don't end with a space
                str[write_index++] = str[read_index];
                space_count++;
                char_count++;
            }
        } else if (str[read_index] != '*') {  // Skip asterisks
            str[write_index++] = str[read_index];
            space_count = 0;
            char_count++;
        }
        read_index++;
    }

    return write_index;
}

static void gps_com_execute_tpms_get_configure_cmd(uint8_t *data_p,
        size_t data_len, uint8_t uart_num,
        void (*ack_send_func_p)(const char *ack_p)) {
    if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
        // wrong params
        return;
    }
    if (data_len < PERIPHERAL_CMD_SET_TPMS_GET_LEN) {
        // not correct message or not ready
        return;
    }

    ack_send_func_p("CMD,OK");

    uint8_t retorno[1100];
    memset(retorno, 0, sizeof retorno);
    int buffer_size = 0;

    config_file_t *config_file = config_file_get();

    strcpy(retorno, "TOSERVER,");
    for (int index_comma = 0; index_comma < sizeof config_file->array_tpms; ++index_comma) {
        if (strlen(config_file->array_tpms[index_comma].code) != 0) {
            // Append code
            strncat(retorno, config_file->array_tpms[index_comma].code, 12);
            strcat(retorno, ",");

            // Append pressure_min
            char temp[10];
            snprintf(temp, sizeof(temp), "%d,", config_file->array_tpms[index_comma].pressure_min);
            strcat(retorno, temp);

            // Append pressure_max
            snprintf(temp, sizeof(temp), "%d,", config_file->array_tpms[index_comma].pressure_max);
            strcat(retorno, temp);

            // Append temperature_min
            snprintf(temp, sizeof(temp), "%d,", config_file->array_tpms[index_comma].temperature_min);
            strcat(retorno, temp);

            // Append temperature_max
            snprintf(temp, sizeof(temp), "%d", config_file->array_tpms[index_comma].temperature_max);
            strcat(retorno, temp);

            if (index_comma < sizeof config_file->array_tpms - 1 &&
                strlen(config_file->array_tpms[index_comma + 1].code) != 0) {
                strcat(retorno, ",");
            }
        } else {
            break;
        }
    }

    strcat(retorno, "*");

    buffer_size = strlen(retorno);

    queue_message_put(gps_com.tx_queue_message_handle, NULL, 0, retorno,
            buffer_size, NULL, 0, 0);

    // send ACK
    if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
        audio_debug_com_clr_log();
        ack_send_func_p("CMD,OK");
        queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
                retorno, buffer_size, NULL, 0, 0);
    }
}

static void gps_com_execute_driver_get_configure_cmd(uint8_t *data_p,
        size_t data_len, uint8_t uart_num,
        void (*ack_send_func_p)(const char *ack_p)) {
    if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
        // wrong params
        return;
    }
    if (data_len < PERIPHERAL_CMD_SET_GET_DRIVER_LEN) {
        // not correct message or not ready
        return;
    }

    ack_send_func_p("CMD,OK");

    // send ACK
    if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
        audio_debug_com_clr_log();
        ack_send_func_p("CMD,OK");
    }

    uint8_t retorno[1100];
    memset(retorno, 0, sizeof retorno);
    int buffer_size = 1;
    // save config
    config_file_t *config_file = config_file_get();

    config_mutex_take(osWaitForever);
    {
        int sendLine = 1;

        strcpy(retorno, "TOSERVER,");
        for (int index_comma = 0;
                index_comma < sizeof config_file->array_driver_id;
                ++index_comma) {

            if (index_comma >= 80 * sendLine) {
                sendLine++;

                // Remove trailing comma if present
                int len = strlen(retorno);
                if (len > 0 && retorno[len - 1] == ',') {
                    retorno[len - 1] = '\0';
                }

                strncat(retorno, "*", 1);

                for (buffer_size = 0; buffer_size < sizeof retorno;
                        ++buffer_size) {
                    if (retorno[buffer_size] == NULL)
                        break;
                }

                queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
                        retorno, buffer_size, NULL, 0, 0);

                if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
                    queue_message_put(audio_debug_com_get_tx_messages_queue(),
                    NULL, 0, retorno, buffer_size, NULL, 0, 0);
                }

                memset(retorno, 0, sizeof retorno);
                strcpy(retorno, "TOSERVER,");
            }

            if (strlen(config_file->array_driver_id[index_comma].driver_id)
                    != 0) {
                strncat(retorno,
                        config_file->array_driver_id[index_comma].driver_id,
                        11);

                if (index_comma < sizeof config_file->array_driver_id - 1
                        && strlen(
                                config_file->array_driver_id[index_comma + 1].driver_id)
                                != 0) {
                    strncat(retorno, ",", 1);
                }
            } else {
                // Remove trailing comma if present
                int len = strlen(retorno);
                if (len > 0 && retorno[len - 1] == ',') {
                    retorno[len - 1] = '\0';
                }

                strncat(retorno, "*", 1);

                for (buffer_size = 0; buffer_size < sizeof retorno;
                        ++buffer_size) {
                    if (retorno[buffer_size] == NULL)
                        break;
                }

                queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
                        retorno, buffer_size, NULL, 0, 0);

                if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
                    queue_message_put(audio_debug_com_get_tx_messages_queue(),
                    NULL, 0, retorno, buffer_size, NULL, 0, 0);
                }
                break;
            }
        }
    }

    config_mutex_give();
}

static void gps_com_execute_activity_get_configure_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_GET_ACTIVITY_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t retorno[720];
	memset(retorno, 0, sizeof retorno);
	int buffer_size = 1;
	// save config
	config_file_t *config_file = config_file_get();

	config_mutex_take(osWaitForever);
	{
		strcpy(retorno, "TOSERVER,");
		for (int index_comma = 0;
				index_comma < sizeof config_file->array_activity_code;
				++index_comma) {

			if (strlen(
					config_file->array_activity_code[index_comma].activity_code)
					!= 0) {
				strncat(retorno,
						config_file->array_activity_code[index_comma].activity_code,
						2);

				if (index_comma < sizeof config_file->array_activity_code
						&& strlen(
								config_file->array_activity_code[index_comma + 1].activity_code)
								!= 0) {
					strncat(retorno, ",", 1);
				}
			} else {
				break;
			}
		}

		strncat(retorno, "*", 1);

		for (buffer_size = 0; buffer_size < sizeof retorno; ++buffer_size) {
			if (retorno[buffer_size] == NULL)
				break;
		}
	}

	queue_message_put(gps_com.tx_queue_message_handle, NULL, 0, retorno,
			buffer_size, NULL, 0, 0);

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
		queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
				retorno, buffer_size, NULL, 0, 0);
	}
}

static void gps_com_execute_geo_get_configure_cmd(uint8_t *data_p,
        size_t data_len, uint8_t uart_num,
        void (*ack_send_func_p)(const char *ack_p)) {
    if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
        // wrong params
        return;
    }
    if (data_len < PERIPHERAL_CMD_SET_GEOGET_LEN) {
        // not correct message or not ready
        return;
    }

    ack_send_func_p("CMD,OK");

    uint8_t retorno_circular[900];
    uint8_t retorno_rectangular[900];
    int buffer_size_circular = 0;
    int buffer_size_rectangular = 0;
    config_file_t *config_file = config_file_get();

    config_mutex_take(osWaitForever);
    {
        // Prepare circular geofence response
        memset(retorno_circular, 0, sizeof retorno_circular);
        strcpy(retorno_circular, "TOSERVER,GEOC,");
        for (int index_comma = 0; index_comma < sizeof config_file->array_geofence_circular / sizeof(geofence_circular); ++index_comma) {
            if (strlen(config_file->array_geofence_circular[index_comma].code) != 0) {
                char temp[100];
                snprintf(temp, sizeof(temp), "%.4s,%d,%d,%f,%f,%d,",
                    config_file->array_geofence_circular[index_comma].code,
                    config_file->array_geofence_circular[index_comma].in_out,
                    config_file->array_geofence_circular[index_comma].speed_max,
                    config_file->array_geofence_circular[index_comma].central_point_lat,
                    config_file->array_geofence_circular[index_comma].central_point_long,
                    config_file->array_geofence_circular[index_comma].radius);
                strcat(retorno_circular, temp);
            } else {
                break;
            }
        }
        // Remove trailing comma if exists
        size_t len = strlen(retorno_circular);
        if (len > 0 && retorno_circular[len-1] == ',') {
        	retorno_circular[len-1] = '*';
        } else {
            strcat(retorno_circular, "*");
        }

        buffer_size_circular = strlen(retorno_circular);
        queue_message_put(gps_com.tx_queue_message_handle, NULL, 0, retorno_circular,
                buffer_size_circular, NULL, 0, 0);

        // Prepare rectangular geofence response
        memset(retorno_rectangular, 0, sizeof retorno_rectangular);
        strcpy(retorno_rectangular, "TOSERVER,GEOR,");
        for (int index_comma = 0; index_comma < sizeof config_file->array_geofence_rectangular / sizeof(geofence_rectangular); ++index_comma) {
            if (strlen(config_file->array_geofence_rectangular[index_comma].code) != 0) {
                char temp[150];
                snprintf(temp, sizeof(temp), "%.4s,%d,%d,%f,%f,%f,%f,",
                    config_file->array_geofence_rectangular[index_comma].code,
                    config_file->array_geofence_rectangular[index_comma].in_out,
                    config_file->array_geofence_rectangular[index_comma].speed_max,
                    config_file->array_geofence_rectangular[index_comma].upper_left_point_lat,
                    config_file->array_geofence_rectangular[index_comma].upper_left_point_long,
                    config_file->array_geofence_rectangular[index_comma].lower_right_point_lat,
                    config_file->array_geofence_rectangular[index_comma].lower_right_point_long);
                strcat(retorno_rectangular, temp);
            } else {
                break;
            }
        }
        // Remove trailing comma if exists
        len = strlen(retorno_rectangular);
        if (len > 0 && retorno_rectangular[len-1] == ',') {
        	retorno_rectangular[len-1] = '*';
        } else {
            strcat(retorno_rectangular, "*");
        }

        buffer_size_rectangular = strlen(retorno_rectangular);
        queue_message_put(gps_com.tx_queue_message_handle, NULL, 0, retorno_rectangular,
                buffer_size_rectangular, NULL, 0, 0);
    }

    config_mutex_give();

    // send ACK
    if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
        audio_debug_com_clr_log();
        ack_send_func_p("CMD,OK");
        queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
                retorno_circular, buffer_size_circular, NULL, 0, 0);
        queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
                retorno_rectangular, buffer_size_rectangular, NULL, 0, 0);
    }
}

static void gps_com_execute_config_get_configure_cmd(uint8_t *data_p,
        size_t data_len, uint8_t uart_num,
        void (*ack_send_func_p)(const char *ack_p)) {
    if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
        // wrong params
        return;
    }
    if (data_len < PERIPHERAL_CMD_SET_CONFIG_GET_LEN) {
        // not correct message or not ready
        return;
    }

    ack_send_func_p("CMD,OK");

    uint8_t retorno[600];  // Increased buffer size to accommodate new parameters
    memset(retorno, 0, sizeof retorno);
    int buffer_size = 0;

    config_file_t *config_file = config_file_get();

    snprintf(retorno, sizeof(retorno),
             "TOSERVER,RPMVD,%u,%u,RPMAM,%u,%u,RPMVM,%u,%u,DHL,%u,%u,%u,RPMMAX,%u,ENGTEMP,%u,TURBO,%u,"
             "PULSE,%u,SPEED,%u,VEL1,%u,%u,%u,%u,VEL2,%u,%u,%u,%u,DHL_STATUS,%u,RPMDEF,%u,%u,%u,%u,"
             "TURBODEF,%u,FUELOUT,%u,FUELIN,%u,ROLL,%u,"
             "IN3,%u,%u,%u,%u,%u,IN4,%u,%u,%u,%u,%u,IN5,%u,%u,%u,%u,%u,"
             "IN6,%u,%u,%u,%u,%u,IN7,%u,%u,%u,%u,%u,IN8,%u,%u,%u,%u,%u,"
             "IN9,%u,%u,%u,%u,%u,IN10,%u,%u,%u,%u,%u,"
             "DRIVER,%u,IDLE_CUT,%u,%u,%u*",
             config_file->rpm_green_min, config_file->rpm_green_max,
             config_file->rpm_yellow_min, config_file->rpm_yellow_max,
             config_file->rpm_red_min, config_file->rpm_red_max,
             config_file->downhill_rpm_max, config_file->downhill_speed_min, config_file->downhill_seconds_min,
             config_file->rpm_max, config_file->temperature_engine_max, config_file->turbo_pressure_max,
             config_file->pulse_enabled, config_file->speed_type,
             config_file->speed_1_speed_beep, config_file->speed_1_speed_alarms, config_file->speed_1_seconds_min, config_file->speed_1_activate_out2,
             config_file->speed_2_speed_beep, config_file->speed_2_speed_alarms, config_file->speed_2_seconds_min, config_file->speed_2_activate_out2,
             config_file->downhill_enabled,
             config_file->rpm_green_count, config_file->rpm_yellow_count, config_file->rpm_red_count, config_file->downhill_count,
             config_file->turbo_count, config_file->fuel_threshold_out, config_file->fuel_threshold_in, config_file->roll_max,
             config_file->in3_enabled, config_file->in3_notify_when_enabled, config_file->in3_notify_when_disabled, config_file->in3_delay_time, config_file->in3_debounce_time,
             config_file->in4_enabled, config_file->in4_notify_when_enabled, config_file->in4_notify_when_disabled, config_file->in4_delay_time, config_file->in4_debounce_time,
             config_file->in5_enabled, config_file->in5_notify_when_enabled, config_file->in5_notify_when_disabled, config_file->in5_delay_time, config_file->in5_debounce_time,
             config_file->in6_enabled, config_file->in6_notify_when_enabled, config_file->in6_notify_when_disabled, config_file->in6_delay_time, config_file->in6_debounce_time,
             config_file->in7_enabled, config_file->in7_notify_when_enabled, config_file->in7_notify_when_disabled, config_file->in7_delay_time, config_file->in7_debounce_time,
             config_file->in8_enabled, config_file->in8_notify_when_enabled, config_file->in8_notify_when_disabled, config_file->in8_delay_time, config_file->in8_debounce_time,
             config_file->in9_enabled, config_file->in9_notify_when_enabled, config_file->in9_notify_when_disabled, config_file->in9_delay_time, config_file->in9_debounce_time,
             config_file->in10_enabled, config_file->in10_notify_when_enabled, config_file->in10_notify_when_disabled, config_file->in10_delay_time, config_file->in10_debounce_time,
             config_file->unidentified_driver_alarm_enabled,
             config_file->idle_engine_cut_enabled, config_file->idle_engine_cut_timer, config_file->idle_engine_cut_period);

    buffer_size = strlen(retorno);

    queue_message_put(gps_com.tx_queue_message_handle, NULL, 0, retorno,
            buffer_size, NULL, 0, 0);

    // send ACK
    if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
        audio_debug_com_clr_log();
        ack_send_func_p("CMD,OK");
        queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
                retorno, buffer_size, NULL, 0, 0);
    }
}

static void gps_com_execute_config_set_cmd(uint8_t *data_p, size_t data_len,
        uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
    if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
        // wrong params
        return;
    }
    if (data_len < PERIPHERAL_CMD_SET_CFG_LEN) {
        // not correct message or not ready
        return;
    }

    ack_send_func_p("CMD,OK");

    config_file_t *config_file = config_file_get();

    // Parse the command and update configuration
    char *token;
    char *saveptr;
    char *cmd = (char *)data_p + PERIPHERAL_CMD_SET_CFG_LEN;

    token = strtok_r(cmd, ",", &saveptr);
    while (token != NULL) {
        if (strcmp(token, "RPMVD") == 0) {
            config_file->rpm_green_min = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->rpm_green_max = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "RPMAM") == 0) {
            config_file->rpm_yellow_min = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->rpm_yellow_max = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "RPMVM") == 0) {
            config_file->rpm_red_min = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->rpm_red_max = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "DHL") == 0) {
            config_file->downhill_rpm_max = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->downhill_speed_min = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->downhill_seconds_min = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "RPMMAX") == 0) {
            config_file->rpm_max = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "ENGTEMP") == 0) {
            config_file->temperature_engine_max = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "TURBO") == 0) {
            config_file->turbo_pressure_max = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "PULSE") == 0) {
            config_file->pulse_enabled = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "SPEED") == 0) {
            config_file->speed_type = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "VEL1") == 0) {
            config_file->speed_1_speed_beep = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->speed_1_speed_alarms = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->speed_1_seconds_min = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->speed_1_activate_out2 = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "VEL2") == 0) {
            config_file->speed_2_speed_beep = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->speed_2_speed_alarms = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->speed_2_seconds_min = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->speed_2_activate_out2 = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "DHL_STATUS") == 0) {
            config_file->downhill_enabled = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "RPMDEF") == 0) {
            config_file->rpm_green_count = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->rpm_yellow_count = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->rpm_red_count = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->downhill_count = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "TURBODEF") == 0) {
            config_file->turbo_count = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "FUELOUT") == 0) {
            config_file->fuel_threshold_out = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "FUELIN") == 0) {
            config_file->fuel_threshold_in = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "ROLL") == 0) {
            config_file->roll_max = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strncmp(token, "IN", 2) == 0) {
            int in_num = atoi(token + 2);
            if (in_num >= 3 && in_num <= 10) {
                uint8_t *enabled = &config_file->in3_enabled + (in_num - 3) * 5;
                *enabled = atoi(strtok_r(NULL, ",", &saveptr));
                *(enabled + 1) = atoi(strtok_r(NULL, ",", &saveptr));
                *(enabled + 2) = atoi(strtok_r(NULL, ",", &saveptr));
                *(enabled + 3) = atoi(strtok_r(NULL, ",", &saveptr));
                *(enabled + 4) = atoi(strtok_r(NULL, ",", &saveptr));
            }
        } else if (strcmp(token, "DRIVER") == 0) {
            config_file->unidentified_driver_alarm_enabled = atoi(strtok_r(NULL, ",", &saveptr));
        } else if (strcmp(token, "IDLE_CUT") == 0) {
            config_file->idle_engine_cut_enabled = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->idle_engine_cut_timer = atoi(strtok_r(NULL, ",", &saveptr));
            config_file->idle_engine_cut_period = atoi(strtok_r(NULL, ",", &saveptr));
        }
        token = strtok_r(NULL, ",", &saveptr);
    }

    // Save the updated configuration
    save_config();

    // Update relevant systems
    can_com_set_counters(config_file->rpm_green_count,
            config_file->rpm_yellow_count,
            config_file->rpm_red_count,
            config_file->downhill_count);

    can_com_set_turbo_counter(config_file->turbo_count);

    // send ACK
    if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
        audio_debug_com_clr_log();
        ack_send_func_p("CMD,OK");
    }
}

static void gps_com_execute_txt_uart_configure_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_TXT_UART_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[1000];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_TXT_UART_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len - 1);

	// save config
	char delim[] = ",";
	char *end_buff;
	char *split_comma = strtok_r(buff, delim, &end_buff);
	int index = 0;

	int uart_selected = 2;

	while (split_comma != NULL) {
		if (index == 0) {
			uart_selected = atoi(split_comma);

			if (uart_selected == 1) {
				queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
						end_buff, strlen(end_buff), NULL, 0, 0);
			} else if (uart_selected == 2) {
				// Add CR/LF to any message to UART2
				size_t new_buff_size = strlen(end_buff) + 3; // +2 for CR/LF, +1 for null terminator
				char *new_buff = malloc(new_buff_size);  // Allocate buffer
				if (new_buff) {
					strncpy(new_buff, end_buff, new_buff_size - 1); // Copy with bounds checking
					new_buff[new_buff_size - 1] = '\0'; // Ensure null termination
					strncat(new_buff, "\r\n", 2); // Concatenate with bounds checking
					queue_message_put(can_com_get_tx_messages_queue(), NULL, 0,
							new_buff, strlen(new_buff), NULL, 0, 0);
					free(new_buff);
				}
			} else if (uart_selected == 3) {
				queue_message_put(keyboard_com_get_tx_messages_queue(),
				NULL, 0, end_buff, strlen(end_buff), NULL, 0, 0);
			} else if (uart_selected == 4) {
				queue_message_put(audio_debug_com_get_tx_messages_queue(),
				NULL, 0, end_buff, strlen(end_buff), NULL, 0, 0);
			} else if (uart_selected == 5) {
				queue_message_put(tpms_com_get_tx_messages_queue(), NULL, 0,
						end_buff, strlen(end_buff), NULL, 0, 0);
			} else if (uart_selected == 6) {
				queue_message_put(tilt_sensor_com_get_tx_messages_queue(),
				NULL, 0, end_buff, strlen(end_buff), NULL, 0, 0);
			}

			break;
		}

		split_comma = strtok_r(NULL, delim, &end_buff);
		index++;
	}

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_rpm_limit_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_RPM_LIMIT_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_RPM_LIMIT_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->rpm_max = atoi(buff);
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_fuel_out_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_FUEL_OUT_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_FUEL_OUT_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->fuel_threshold_out = atoi(buff);
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_fuel_in_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_FUEL_IN_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_FUEL_IN_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->fuel_threshold_in = atoi(buff);
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_turbo_limit_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_TURBO_LIMIT_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_TURBO_LIMIT_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->turbo_pressure_max = atoi(buff);
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_engtemp_limit_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_ENGTEMP_LIMIT_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_ENGTEMP_LIMIT_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->temperature_engine_max = atoi(buff);
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_in_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_IN_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_IN_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		int selected_port = 3;
		int index_item = 0;
		while (split_comma != NULL) {
			if (index_item == 0) {
				selected_port = atoi(split_comma);
				index_item++;
			} else if (index_item == 1) {
				if (selected_port == 3)
					config_file->in3_enabled = atoi(split_comma);
				else if (selected_port == 4)
					config_file->in4_enabled = atoi(split_comma);
				else if (selected_port == 5)
					config_file->in5_enabled = atoi(split_comma);
				else if (selected_port == 6)
					config_file->in6_enabled = atoi(split_comma);
				else if (selected_port == 7)
					config_file->in7_enabled = atoi(split_comma);
				else if (selected_port == 8)
					config_file->in8_enabled = atoi(split_comma);
				else if (selected_port == 9)
					config_file->in9_enabled = atoi(split_comma);
				else if (selected_port == 10)
					config_file->in10_enabled = atoi(split_comma);

				index_item++;
			} else if (index_item == 2) {
				if (selected_port == 3)
					config_file->in3_notify_when_enabled = atoi(split_comma);
				else if (selected_port == 4)
					config_file->in4_notify_when_enabled = atoi(split_comma);
				else if (selected_port == 5)
					config_file->in5_notify_when_enabled = atoi(split_comma);
				else if (selected_port == 6)
					config_file->in6_notify_when_enabled = atoi(split_comma);
				else if (selected_port == 7)
					config_file->in7_notify_when_enabled = atoi(split_comma);
				else if (selected_port == 8)
					config_file->in8_notify_when_enabled = atoi(split_comma);
				else if (selected_port == 9)
					config_file->in9_notify_when_enabled = atoi(split_comma);
				else if (selected_port == 10)
					config_file->in10_notify_when_enabled = atoi(split_comma);

				index_item++;
			} else if (index_item == 3) {
				if (selected_port == 3)
					config_file->in3_notify_when_disabled = atoi(split_comma);
				else if (selected_port == 4)
					config_file->in4_notify_when_disabled = atoi(split_comma);
				else if (selected_port == 5)
					config_file->in5_notify_when_disabled = atoi(split_comma);
				else if (selected_port == 6)
					config_file->in6_notify_when_disabled = atoi(split_comma);
				else if (selected_port == 7)
					config_file->in7_notify_when_disabled = atoi(split_comma);
				else if (selected_port == 8)
					config_file->in8_notify_when_disabled = atoi(split_comma);
				else if (selected_port == 9)
					config_file->in9_notify_when_disabled = atoi(split_comma);
				else if (selected_port == 10)
					config_file->in10_notify_when_disabled = atoi(split_comma);

				index_item++;
			} else if (index_item == 4) {
				if (selected_port == 3)
					config_file->in3_delay_time = atoi(split_comma);
				else if (selected_port == 4)
					config_file->in4_delay_time = atoi(split_comma);
				else if (selected_port == 5)
					config_file->in5_delay_time = atoi(split_comma);
				else if (selected_port == 6)
					config_file->in6_delay_time = atoi(split_comma);
				else if (selected_port == 7)
					config_file->in7_delay_time = atoi(split_comma);
				else if (selected_port == 8)
					config_file->in8_delay_time = atoi(split_comma);
				else if (selected_port == 9)
					config_file->in9_delay_time = atoi(split_comma);
				else if (selected_port == 10)
					config_file->in10_delay_time = atoi(split_comma);

				index_item++;
			} else if (index_item == 5) {
				if (selected_port == 3)
					config_file->in3_debounce_time = atoi(split_comma);
				else if (selected_port == 4)
					config_file->in4_debounce_time = atoi(split_comma);
				else if (selected_port == 5)
					config_file->in5_debounce_time = atoi(split_comma);
				else if (selected_port == 6)
					config_file->in6_debounce_time = atoi(split_comma);
				else if (selected_port == 7)
					config_file->in7_debounce_time = atoi(split_comma);
				else if (selected_port == 8)
					config_file->in8_debounce_time = atoi(split_comma);
				else if (selected_port == 9)
					config_file->in9_debounce_time = atoi(split_comma);
				else if (selected_port == 10)
					config_file->in10_debounce_time = atoi(split_comma);

				break;
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_speed_conversion_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_SPEED_CONVERSION_MIN_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_SPEED_CONVERSION_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		if (atoi((char const*) buff) == 1) {
			config_file->speed_type = 1;
		} else {
			config_file->speed_type = 0;
		}
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_add_driver_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_ADD_DRIVER_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_ADD_DRIVER_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);
	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		while (split_comma != NULL) {

			if (strlen(split_comma) < 11) {
				split_comma = strtok_r(NULL, delim, &end_buff);
				continue;
			}

			for (int index_comma = 0;
					index_comma < sizeof config_file->array_driver_id;
					++index_comma) {
				if (strlen(config_file->array_driver_id[index_comma].driver_id)
						== 0) {
					strncpy(config_file->array_driver_id[index_comma].driver_id,
							split_comma, 11);
					break;
				} else {
					if (strncmp(
							config_file->array_driver_id[index_comma].driver_id,
							split_comma, 11) == 0) {
						break;
					}
				}
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_delete_driver_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_DELETE_DRIVER_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_DELETE_DRIVER_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);
	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		while (split_comma != NULL) {
			for (int index_comma = 0;
					index_comma < sizeof config_file->array_driver_id;
					++index_comma) {
				if (strlen(config_file->array_driver_id[index_comma].driver_id)
						== 0) {
					break;
				}

				if (strncmp(config_file->array_driver_id[index_comma].driver_id,
						split_comma, 11) == 0) {
					for (int index_adjust = index_comma;
							index_comma < sizeof config_file->array_driver_id;
							++index_adjust) {

						if (strlen(
								config_file->array_driver_id[index_adjust + 1].driver_id)
								== 0) {
							memset(
									config_file->array_driver_id[index_adjust].driver_id,
									0, 11);
							break;
						}

						strncpy(
								config_file->array_driver_id[index_adjust].driver_id,
								config_file->array_driver_id[index_adjust + 1].driver_id,
								11);

					}
				}
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_delete_tpms_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_DELETE_TPMS_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_DELETE_TPMS_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);
	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		while (split_comma != NULL) {
			for (int index_comma = 0;
					index_comma < sizeof config_file->array_tpms;
					++index_comma) {
				if (strlen(config_file->array_tpms[index_comma].code) == 0) {
					break;
				}

				if (strncmp(config_file->array_tpms[index_comma].code,
						split_comma, 12) == 0) {
					for (int index_adjust = index_comma;
							index_comma < sizeof config_file->array_tpms;
							++index_adjust) {

						if (strlen(
								config_file->array_tpms[index_adjust + 1].code)
								== 0) {
							memset(config_file->array_tpms[index_adjust].code,
									0,
									sizeof config_file->array_tpms[index_adjust].code);
							memset(
									config_file->array_tpms[index_adjust].pressure_min,
									0,
									sizeof config_file->array_tpms[index_adjust].pressure_min);
							memset(
									config_file->array_tpms[index_adjust].pressure_max,
									0,
									sizeof config_file->array_tpms[index_adjust].pressure_max);
							memset(
									config_file->array_tpms[index_adjust].temperature_min,
									0,
									sizeof config_file->array_tpms[index_adjust].temperature_min);
							memset(
									config_file->array_tpms[index_adjust].temperature_max,
									0,
									sizeof config_file->array_tpms[index_adjust].temperature_max);

							break;
						}

						memcpy(config_file->array_tpms[index_adjust].code,
								config_file->array_tpms[index_adjust + 1].code,
								sizeof config_file->array_tpms[index_adjust].code);
						memcpy(
								config_file->array_tpms[index_adjust].pressure_min,
								config_file->array_tpms[index_adjust + 1].pressure_min,
								sizeof config_file->array_tpms[index_adjust].pressure_min);
						memcpy(
								config_file->array_tpms[index_adjust].pressure_max,
								config_file->array_tpms[index_adjust + 1].pressure_max,
								sizeof config_file->array_tpms[index_adjust].pressure_max);
						memcpy(
								config_file->array_tpms[index_adjust].temperature_min,
								config_file->array_tpms[index_adjust + 1].temperature_min,
								sizeof config_file->array_tpms[index_adjust].temperature_min);
						memcpy(
								config_file->array_tpms[index_adjust].temperature_max,
								config_file->array_tpms[index_adjust + 1].temperature_max,
								sizeof config_file->array_tpms[index_adjust].temperature_max);
					}
				}
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_delete_geo_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_GEODEL_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_GEODEL_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);
	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		bool item_found = false;

		while (split_comma != NULL) {
			for (int index_comma = 0;
					index_comma < sizeof config_file->array_geofence_rectangular;
					++index_comma) {
				if (strlen(
						config_file->array_geofence_rectangular[index_comma].code)
						== 0) {
					break;
				}

				if (strncmp(
						config_file->array_geofence_rectangular[index_comma].code,
						split_comma, 4) == 0 || item_found) {
					item_found = true;
					strncpy(
							config_file->array_geofence_rectangular[index_comma].code,
							config_file->array_geofence_rectangular[index_comma
									+ 1].code, 4);

					config_file->array_geofence_rectangular[index_comma].in_out =
							config_file->array_geofence_rectangular[index_comma
									+ 1].in_out;

					config_file->array_geofence_rectangular[index_comma].speed_max =
							config_file->array_geofence_rectangular[index_comma
									+ 1].speed_max;

					config_file->array_geofence_rectangular[index_comma].upper_left_point_lat =
							config_file->array_geofence_rectangular[index_comma
									+ 1].upper_left_point_lat;
					config_file->array_geofence_rectangular[index_comma].upper_left_point_long =
							config_file->array_geofence_rectangular[index_comma
									+ 1].upper_left_point_long;
					config_file->array_geofence_rectangular[index_comma].lower_right_point_lat =
							config_file->array_geofence_rectangular[index_comma
									+ 1].lower_right_point_lat;

					config_file->array_geofence_rectangular[index_comma].lower_right_point_long =
							config_file->array_geofence_rectangular[index_comma
									+ 1].lower_right_point_long;

					if (strlen(
							config_file->array_geofence_rectangular[index_comma
									+ 1].code) == 0) {
						memset(
								config_file->array_geofence_rectangular[index_comma].code,
								0, 4);

						config_file->array_geofence_rectangular[index_comma].in_out =
								0;

						config_file->array_geofence_rectangular[index_comma].speed_max =
								0;

						config_file->array_geofence_rectangular[index_comma].upper_left_point_lat =
								0;

						config_file->array_geofence_rectangular[index_comma].upper_left_point_long =
								0;

						config_file->array_geofence_rectangular[index_comma].lower_right_point_lat =
								0;

						config_file->array_geofence_rectangular[index_comma].lower_right_point_long =
								0;

						break;
					}
				}
			}

			item_found = false;

			for (int index_comma = 0;
					index_comma < sizeof config_file->array_geofence_circular;
					++index_comma) {
				if (strlen(
						config_file->array_geofence_circular[index_comma].code)
						== 0) {
					break;
				}

				if (strncmp(
						config_file->array_geofence_circular[index_comma].code,
						split_comma, 4) == 0 || item_found) {
					item_found = true;
					strncpy(
							config_file->array_geofence_circular[index_comma].code,
							config_file->array_geofence_circular[index_comma + 1].code,
							4);

					config_file->array_geofence_circular[index_comma].in_out =
							config_file->array_geofence_circular[index_comma + 1].in_out;
					config_file->array_geofence_circular[index_comma].speed_max =
							config_file->array_geofence_circular[index_comma + 1].speed_max;

					config_file->array_geofence_circular[index_comma].central_point_lat =
							config_file->array_geofence_circular[index_comma + 1].central_point_lat;

					config_file->array_geofence_circular[index_comma].central_point_long =
							config_file->array_geofence_circular[index_comma + 1].central_point_long;

					config_file->array_geofence_circular[index_comma].radius =
							config_file->array_geofence_circular[index_comma + 1].radius;

					if (strlen(
							config_file->array_geofence_circular[index_comma + 1].code)
							== 0) {
						memset(
								config_file->array_geofence_circular[index_comma].code,
								0, 4);

						config_file->array_geofence_circular[index_comma].in_out =
								0;
						config_file->array_geofence_circular[index_comma].speed_max =
								0;

						config_file->array_geofence_circular[index_comma].central_point_lat =
								0;
						config_file->array_geofence_circular[index_comma].central_point_long =
								0;
						config_file->array_geofence_circular[index_comma].radius =
								0;

						break;
					}
				}
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
		}

		save_config();

		geofence_config_changed = true;
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_delete_activity_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_DELETE_ACTIVITY_CODE_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_DELETE_ACTIVITY_CODE_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);
	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		while (split_comma != NULL) {
			for (int index_comma = 0;
					index_comma < sizeof config_file->array_activity_code;
					++index_comma) {
				if (strlen(
						config_file->array_activity_code[index_comma].activity_code)
						== 0) {
					break;
				}

				if (strncmp(
						config_file->array_activity_code[index_comma].activity_code,
						split_comma, 2) == 0) {
					for (int index_adjust = index_comma;
							index_comma
									< sizeof config_file->array_activity_code;
							++index_adjust) {

						if (strlen(
								config_file->array_activity_code[index_adjust
										+ 1].activity_code) == 0) {
							memset(
									config_file->array_activity_code[index_adjust].activity_code,
									0, 2);
							break;
						}

						strncpy(
								config_file->array_activity_code[index_adjust].activity_code,
								config_file->array_activity_code[index_adjust
										+ 1].activity_code, 2);

					}
				}
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_add_tpms_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_ADD_TPMS_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_ADD_TPMS_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len - 1);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		int index_item = 0;
		for (int index_comma = 0; index_comma < sizeof config_file->array_tpms;
				++index_comma) {
			if (strlen(config_file->array_tpms[index_comma].code) == 0
					|| strncmp(config_file->array_tpms[index_comma].code,
							split_comma, 12) == 0) {
				while (split_comma != NULL) {
					if (index_item == 0) {
						strncpy(config_file->array_tpms[index_comma].code,
								split_comma, 12);
						index_item++;
						split_comma = strtok_r(NULL, delim, &end_buff);
					} else if (index_item == 1) {
						config_file->array_tpms[index_comma].pressure_min =
								atoi(split_comma);
						index_item++;
						split_comma = strtok_r(NULL, delim, &end_buff);
					} else if (index_item == 2) {
						config_file->array_tpms[index_comma].pressure_max =
								atoi(split_comma);
						index_item++;
						split_comma = strtok_r(NULL, delim, &end_buff);
					} else if (index_item == 3) {
						config_file->array_tpms[index_comma].temperature_min =
								atoi(split_comma);
						index_item++;
						split_comma = strtok_r(NULL, delim, &end_buff);
					} else if (index_item == 4) {
						config_file->array_tpms[index_comma].temperature_max =
								atoi(split_comma);
						index_item = 0;
						split_comma = strtok_r(NULL, delim, &end_buff);
						break;
					}
				}
				if (split_comma == NULL) {
					break;
				}
			}
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_add_activity_code_cmd(uint8_t *data_p,
		size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);

		while (split_comma != NULL) {
			for (int index_comma = 0;
					index_comma < sizeof config_file->array_activity_code;
					++index_comma) {
				if (strncmp(
						config_file->array_activity_code[index_comma].activity_code,
						split_comma, 2) == 0) {
					break;
				}

				if (strlen(
						config_file->array_activity_code[index_comma].activity_code)
						== 0) {
					strncpy(
							config_file->array_activity_code[index_comma].activity_code,
							split_comma, 2);
					break;
				}
			}

			split_comma = strtok_r(NULL, delim, &end_buff);
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_rpm_clr_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_CLR_RPM_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	// clr rpm counters
	config_mutex_take(osWaitForever);
	{
		config_file_t *config_file = config_file_get();
		config_file->rpm_green_count = 0;
		config_file->rpm_yellow_count = 0;
		config_file->rpm_red_count = 0;
		config_file->downhill_count = 0;

		// clr rpm counters
		can_com_set_counters(0, 0, 0, 0);

		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_rollover_set_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE_MIN_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint32_t angle = 0;
	int num = 0;
	uint8_t buff[10];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);
	num = atoi((char const*) buff);
	angle = (uint32_t) num;

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{
		config_file->roll_max = angle;
		save_config();
	}
	config_mutex_give();
	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_pulse_set_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {

	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_PULSE_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_PULSE_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}
	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	config_file_t *config_file = config_file_get();

	// save config
	config_mutex_take(osWaitForever);
	{

		char delim[] = ",";
		char *end_buff;
		char *split_comma = strtok_r(buff, delim, &end_buff);
		int index = 0;

		while (split_comma != NULL) {
			if (index == 0)
				config_file->pulse_enabled = atoi(split_comma);
			else if (index == 1)
				config_file->pulse_rpm_factor = atoi(split_comma);
			else if (index == 2)
				config_file->pulse_speed_factor = atoi(split_comma);

			split_comma = strtok_r(NULL, delim, &end_buff);
			index++;
		}

		save_config();
	}

	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_dhl_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_DHL_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_DHL_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		if (atoi((char const*) buff) == 1) {
			config_file->downhill_enabled = 1;
		} else {
			config_file->downhill_enabled = 0;
		}
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_driver_alarm_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_SET_DRIVER_ALARM_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	uint8_t buff[500];

	// get enable/disable
	char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_DRIVER_ALARM_LEN];
	int len = data_len - (start_p - (char*) data_p);
	if (len <= 0) {
		// not correct command
		return;
	}

	memset(buff, 0, sizeof(buff));
	memcpy(buff, start_p, len);

	// save config
	config_mutex_take(osWaitForever);
	{
		if (atoi((char const*) buff) == 1) {
			config_file->unidentified_driver_alarm_enabled = 1;
		} else {
			config_file->unidentified_driver_alarm_enabled = 0;
		}
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_set_engine_cut_cmd(uint8_t *data_p, size_t data_len,
        uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
    if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
        // wrong params
        return;
    }
    if (data_len < PERIPHERAL_CMD_SET_ENGINE_CUT_LEN) {
        // not correct message or not ready
        return;
    }

    ack_send_func_p("CMD,OK");

    config_file_t *config_file = config_file_get();
    uint8_t buff[500];

    // get enable/disable, timer value, and period
    char *start_p = (char*) &data_p[PERIPHERAL_CMD_SET_ENGINE_CUT_LEN];
    int len = data_len - (start_p - (char*) data_p);
    if (len <= 0) {
        // not correct command
        return;
    }

    memset(buff, 0, sizeof(buff));
    memcpy(buff, start_p, len);

    // save config
    config_mutex_take(osWaitForever);
    {
        char *token = strtok((char*)buff, ",");
        if (token != NULL) {
            config_file->idle_engine_cut_enabled = atoi(token);
            token = strtok(NULL, ",");
            if (token != NULL) {
                config_file->idle_engine_cut_timer = atoi(token);
                token = strtok(NULL, ",");
                if (token != NULL) {
                    config_file->idle_engine_cut_period = atoi(token);
                }
            }
        }
        save_config();
    }
    config_mutex_give();

    // send ACK
    if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
        audio_debug_com_clr_log();
        ack_send_func_p("CMD,OK");
    }
}

static void gps_com_execute_odo_clr_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_CLR_ODO_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();

	config_mutex_take(osWaitForever);
	{

		// clr odometer cntr
		config_file->odometer_count = 0;
		rpm_set_odometer_ablosute_cnt(0);

		save_config();
	}
	config_mutex_give();
	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_turbo_clr_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_CLR_TURBO_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();

	config_mutex_take(osWaitForever);
	{

		// clr TURBO cntr
		config_file->turbo_count = 0;
		can_com_set_turbo_counter(0);

		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_eng_clr_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_CLR_ENG_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");

	config_file_t *config_file = config_file_get();
	config_mutex_take(osWaitForever);
	{
		//		// clr eng cntr
		config_file->movement_timer = 0;
		config_file->stopped_timer = 0;

		can_com_set_eng_counters(0, 0);
		save_config();
	}
	config_mutex_give();

	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
	}
}

static void gps_com_execute_get_fw_cmd(uint8_t *data_p, size_t data_len,
                                       uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
    if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
        // wrong params
        return;
    }
    if (data_len < PERIPHERAL_CMD_GET_FW_LEN) {
        // not correct message or not ready
        return;
    }

    // send ack
    ack_send_func_p("CMD,OK");

    char response[20];
    int response_len = 0;

    config_mutex_take(osWaitForever);
    {
        config_file_t *config_file = config_file_get();

        snprintf(response, sizeof(response), "TOSERVER,FW%d*", config_file->fw_version);
        response_len = strlen(response);

        // Queue response to GPS COM queue
        queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
                          (uint8_t*)response, response_len, NULL, 0, 0);
    }
    config_mutex_give();

    // Handle audio debug UART
    if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
        audio_debug_com_clr_log();
        ack_send_func_p("CMD,OK");

        // Queue response to audio debug queue
        queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
                          (uint8_t*)response, response_len, NULL, 0, 0);
    }
}

static void gps_com_execute_reboot_cmd(uint8_t *data_p, size_t data_len,
		uint8_t uart_num, void (*ack_send_func_p)(const char *ack_p)) {
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)) {
		// wrong params
		return;
	}
	if (data_len < PERIPHERAL_CMD_REBOOT_LEN) {
		// not correct message or not ready
		return;
	}

	ack_send_func_p("CMD,OK");
	// save current counters
	save_config();
	// send ACK
	if (uart_num == PERIPHERAL_UART_AUDIO_DEBUG_NUM) {
		audio_debug_com_clr_log();
		ack_send_func_p("CMD,OK");
		osDelay(100); // delay transmitting ACK
	}

	// restart system
	NVIC_SystemReset();
}

static void gps_com_ack_send(const char *ack_p) {
	if ((ack_p == NULL) || (strlen(ack_p) == 0)) {
		return;
	}
	// send ACK
	queue_message_put(gps_com.tx_queue_message_handle,
	PERIPHERAL_GPS_RX_START, PERIPHERAL_GPS_RX_START_LEN, (void*) ack_p,
			strlen(ack_p),
			PERIPHERAL_CMD_END, PERIPHERAL_CMD_END_LEN, 0);
}

static void gps_com_rx_thread(void const *argument) {
	gps_com_rx_mail_t *mail_p = NULL;

	while (1) {

		// get mail from gps mail queue
		mail_p = (gps_com_rx_mail_t*) queue_mail_get(
				gps_com.rx_queue_mail_handle,
				osWaitForever);
		if (mail_p != NULL) {

			// log GPS RX data
			audio_debug_com_send_log(mail_p->buff, mail_p->size,
			PERIPHERAL_UART_GPS_NUM, false);

			if (strncmp(mail_p->buff, "CMD,", 4) == 0
					|| strncmp(mail_p->buff, "\r\nCMD,", 4) == 0) {

				int count = 0;
				for (count = 0; count < mail_p->size; ++count) {
					if (mail_p->buff[count] == '*') {
						count++;
						break;
					}
				}

				// process incommin commands
				gps_com_cmd_processing(mail_p->buff, count,
				PERIPHERAL_UART_GPS_NUM, gps_com_ack_send);
			} else if (strncmp(mail_p->buff, "GNGGA,", 6) == 0
					|| strncmp(mail_p->buff, "\r\nGNGGA,", 8) == 0) {

				int count = 0;
				for (count = 0; count < mail_p->size; ++count) {
					if (mail_p->buff[count] == '*') {
						count++;
						break;
					}
				}

				gps_com_gngga_processing(mail_p->buff, count);
			} else if (strncmp(mail_p->buff, "GNRMC,", 6) == 0
					|| strncmp(mail_p->buff, "\r\nGNRMC,", 8) == 0) {

				int count = 0;
				for (count = 0; count < mail_p->size; ++count) {
					if (mail_p->buff[count] == '*') {
						count++;
						break;
					}
				}
				gps_com_gnrmc_processing(mail_p->buff, count);
			}

			// free  mail
			queue_mail_free(gps_com.rx_queue_mail_handle, mail_p);
			mail_p = NULL;
		}

		watchdog_reset();
	}
}

// TX
static void gps_com_tx_cpl_cb(void) {
	osSignalSet(gps_com.tx_task_handle, GPS_COM_TX_END_SIGNAL);
}

static void gps_com_tx_thread(void const *argument) {
	bool is_idle = false;
	bool tx_status = false;
	queue_message_t *message_p = NULL;

	while (1) {

		//osDelay(150);
		gps_speed_refresh();

		message_p = (queue_message_t*) can_com_get_rx_message(&is_idle);
		if ((message_p == NULL)) {
			message_p = (queue_message_t*) tpms_com_get_rx_message(&is_idle);
			if ((message_p == NULL)) {
				message_p = (queue_message_t*) in_ports_get_rx_message();
			}

			if (message_p == NULL) {
				message_p = (queue_message_t*) queue_message_get(
						gps_com.tx_queue_message_handle, 0);

			} else {
				int a = 10;
			}
		}
		// sleep
		if (message_p == NULL) {
			watchdog_reset();
			osDelay(5);
			continue;
		}

		// log GPS TX data
		audio_debug_com_send_log(message_p->buff, message_p->size,
		PERIPHERAL_UART_GPS_NUM, false);

		// transmit
		// clr tx end signal
		osSignalWait(GPS_COM_TX_END_SIGNAL, 0);

		// send message
		tx_status = gps_uart_send(message_p->buff, message_p->size,
				gps_com_tx_cpl_cb);

		// wait end of tx
		if (tx_status) {
			osSignalWait(GPS_COM_TX_END_SIGNAL, osWaitForever);
		}

		// free message
		queue_message_free(message_p);
		message_p = NULL;

		watchdog_reset();

		osDelay(500);
	}
}

static void gps_speed_refresh(void) {
	if ((gps_com.speed_refresh_timeout_ms == 0)) {
		// refresh timeout is expired and fm is not busy

		// compose message
		uint8_t message[10] = { 0xAA, 0x75, 0x55, 0xFF, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00 };
		message[GPS_SPEED_REFRESH_MESSAGE_SPEED_INDEX] = can_com_get_speed();

		// add checksum
		uint8_t checksum = 0;
		for (uint8_t i = 0; i < (sizeof(message) - 1); i++) {
			checksum += message[i];
		}
		message[GPS_SPEED_REFRESH_MESSAGE_SUM_INDEX] = checksum;

		gps_com.speed_refresh_timeout_ms = GPS_SPEED_REFRESH_TIMEOUT_MS;
	}
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
bool gps_com_init(void) {
	// RX
	// create rx mail queue
	osMailQDef(rx_mail_queue, GPS_COM_RX_MAILS_QUEUE_SIZE, gps_com_rx_mail_t);
	gps_com.rx_queue_mail_handle = osMailCreate(osMailQ(rx_mail_queue), NULL);
	if (gps_com.rx_queue_mail_handle == NULL) {
		Error_Handler();
		return false;
	}

	// create rx task
	osThreadDef(gps_rx_task, gps_com_rx_thread, osPriorityNormal, 0,
			configMINIMAL_STACK_SIZE * 30);
	gps_com.rx_task_handle = osThreadCreate(osThread(gps_rx_task), NULL);
	if (gps_com.rx_task_handle == NULL) {
		Error_Handler();
		return false;
	}

	// TX
	// create tx message queue
	uint32_t tmp = 0;
	osMessageQDef(tx_message_queue, GPS_COM_TX_MESSAGES_QUEUE_SIZE, &tmp);
	gps_com.tx_queue_message_handle = osMessageCreate(
			osMessageQ(tx_message_queue),
			NULL);
	if (gps_com.tx_queue_message_handle == NULL) {
		Error_Handler();
		return false;
	}

	// software watchdog init
	gps_com.sw_watchdog_timeout_ms = SOFTWARE_WATCHDOG_TIMEOUT_S_DEFAULT
			* 1000+ SOFTWARE_WATCHDOG_OFFSET_MS;

	// create tx task
	osThreadDef(gps_tx_task, gps_com_tx_thread, osPriorityNormal, 0,
			configMINIMAL_STACK_SIZE * 6);
	gps_com.tx_task_handle = osThreadCreate(osThread(gps_tx_task), NULL);
	if (gps_com.tx_task_handle == NULL) {
		Error_Handler();
		return false;
	}

	// uart init
	if (!gps_uart_init(gps_com_rx_cpl_cb)) {
		Error_Handler();
		return false;
	}

    // Send startup message to GPS tracker
    const char* startup_message = "TOSERVER,STARTUP*";
    queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
                      (uint8_t*)startup_message, strlen(startup_message),
                      NULL, 0, 0);

    gps_com.unidentified_driver_count = 0;
    gps_com.notification_enabled = true;

	return true;
}

void gps_com_time_proc(void) {
	// communication idle timeout
	if (gps_com.idle_timeout_ms > 0) {
		if (gps_com.idle_timeout_ms > 1) {
			gps_com.idle_timeout_ms--;
		} else {
			if (gps_uart_is_rx_started()) {
				// rx ongoing - refresh timeout
				gps_com.idle_timeout_ms = GPS_IDLE_TIMEOUT_MS;
			} else {
				// idle
				// send all data to mail
				if (gps_com.message_buff_size > 0) {
					queue_mail_put(gps_com.rx_queue_mail_handle,
							gps_com.message_buff, gps_com.message_buff_size, 0);
					memset(gps_com.message_buff, 0,
							sizeof(gps_com.message_buff));
					gps_com.message_buff_size = 0;
				}
				gps_com.idle_timeout_ms = 0;
			}
		}
	}

	// speed refreshing timeout
	if (gps_com.speed_refresh_timeout_ms > 0) {
		gps_com.speed_refresh_timeout_ms--;
	}

	// hub ready message timeout
	if (gps_com.hub_ready_timeout_ms > 0) {
		gps_com.hub_ready_timeout_ms--;
	}

	// software watchdog
	if (gps_com.sw_watchdog_timeout_ms > 0) {
		gps_com.sw_watchdog_timeout_ms--;
	}
}

bool gps_com_is_idle(void) {
	if (gps_com.idle_timeout_ms == 0) {
		return true;
	}
	return false;
}

void gps_com_cmd_processing(uint8_t *data_p, size_t data_len, uint8_t uart_num,
		void (*ack_send_func_p)(const char *ack_p)) {

	// check in params
	if ((data_p == NULL) || (data_len == 0) || (ack_send_func_p == NULL)
			|| ((uart_num != PERIPHERAL_UART_GPS_NUM)
					&& (uart_num != PERIPHERAL_UART_AUDIO_DEBUG_NUM))) {
		// wrong params
		return;
	}

	// check command
	if ((data_len >= strlen(PERIPHERAL_CMD_WRITE_DATA))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_WRITE_DATA,
					strlen(PERIPHERAL_CMD_WRITE_DATA)) == 0)) {
		// write data command
		gps_com_execute_data_write_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_RPM))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_RPM,
					strlen(PERIPHERAL_CMD_SET_RPM)) == 0)) {
		// rpm configuration command
		gps_com_execute_rpm_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_KEYBOARD))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_KEYBOARD,
					strlen(PERIPHERAL_CMD_SET_KEYBOARD)) == 0)) {
		// rpm configuration command
		gps_com_execute_keyboard_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_RPM_DEF))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_RPM_DEF,
					strlen(PERIPHERAL_CMD_SET_RPM_DEF)) == 0)) {
		// rpm configuration command
		gps_com_execute_rpm_def_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_TURBO_DEF))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_TURBO_DEF,
					strlen(PERIPHERAL_CMD_SET_TURBO_DEF)) == 0)) {
		// rpm configuration command
		gps_com_execute_turbo_def_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}
	if ((data_len >= strlen(PERIPHERAL_CMD_SET_CLEAR_MERMORY))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_CLEAR_MERMORY,
					strlen(PERIPHERAL_CMD_SET_CLEAR_MERMORY)) == 0)) {
		// rpm configuration command
		gps_com_execute_memory_clear_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_UART))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_UART,
					strlen(PERIPHERAL_CMD_SET_UART)) == 0)) {
		// rpm configuration command
		gps_com_execute_uart_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_OUT))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_OUT,
					strlen(PERIPHERAL_CMD_SET_OUT)) == 0)) {
		// rpm configuration command
		gps_com_execute_out_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_VEL))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_VEL,
					strlen(PERIPHERAL_CMD_SET_VEL)) == 0)) {
		// rpm configuration command
		gps_com_execute_vel_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_MP3))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_MP3,
					strlen(PERIPHERAL_CMD_MP3)) == 0)) {
		gps_com_execute_mp3_cmd(data_p, data_len, uart_num, ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SMS))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SMS,
					strlen(PERIPHERAL_CMD_SMS)) == 0)) {
		gps_com_execute_sms_cmd(data_p, data_len, uart_num, ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_TPMS_GET))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_TPMS_GET,
					strlen(PERIPHERAL_CMD_SET_TPMS_GET)) == 0)) {
		gps_com_execute_tpms_get_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_GET_DRIVER))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_GET_DRIVER,
					strlen(PERIPHERAL_CMD_SET_GET_DRIVER)) == 0)) {
		gps_com_execute_driver_get_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_GET_ACTIVITY))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_GET_ACTIVITY,
					strlen(PERIPHERAL_CMD_SET_GET_ACTIVITY)) == 0)) {
		gps_com_execute_activity_get_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_GEOGET))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_GEOGET,
					strlen(PERIPHERAL_CMD_SET_GEOGET)) == 0)) {
		gps_com_execute_geo_get_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_CFG))
	        && (strncmp((char*) data_p, PERIPHERAL_CMD_SET_CFG,
	                strlen(PERIPHERAL_CMD_SET_CFG)) == 0)) {
	    gps_com_execute_config_set_cmd(data_p, data_len, uart_num,
	            ack_send_func_p);
	    return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_CONFIG_GET))
	        && (strncmp((char*) data_p, PERIPHERAL_CMD_SET_CONFIG_GET,
	                strlen(PERIPHERAL_CMD_SET_CONFIG_GET)) == 0)) {
	    gps_com_execute_config_get_configure_cmd(data_p, data_len, uart_num,
	            ack_send_func_p);
	    return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_GET_FW))
	        && (strncmp((char*) data_p, PERIPHERAL_CMD_GET_FW,
	                strlen(PERIPHERAL_CMD_GET_FW)) == 0)) {
	    gps_com_execute_get_fw_cmd(data_p, data_len, uart_num,
	            ack_send_func_p);
	    return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_TXT_UART))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_TXT_UART,
					strlen(PERIPHERAL_CMD_SET_TXT_UART)) == 0)) {
		// rpm configuration command
		gps_com_execute_txt_uart_configure_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_RPM_LIMIT))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_RPM_LIMIT,
					strlen(PERIPHERAL_CMD_SET_RPM_LIMIT)) == 0)) {
		// rpm configuration command
		gps_com_execute_set_rpm_limit_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_TURBO_LIMIT))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_TURBO_LIMIT,
					strlen(PERIPHERAL_CMD_SET_TURBO_LIMIT)) == 0)) {
		// rpm configuration command
		gps_com_execute_set_turbo_limit_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_FUEL_IN))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_FUEL_IN,
					strlen(PERIPHERAL_CMD_SET_FUEL_IN)) == 0)) {
		// rpm configuration command
		gps_com_execute_set_fuel_in_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_FUEL_OUT))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_FUEL_OUT,
					strlen(PERIPHERAL_CMD_SET_FUEL_OUT)) == 0)) {
		// rpm configuration command
		gps_com_execute_set_fuel_out_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_ENGTEMP_LIMIT))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_ENGTEMP_LIMIT,
					strlen(PERIPHERAL_CMD_SET_ENGTEMP_LIMIT)) == 0)) {
		// rpm configuration command
		gps_com_execute_set_engtemp_limit_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_CLR_RPM))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_CLR_RPM,
					strlen(PERIPHERAL_CMD_CLR_RPM)) == 0)) {
		// rpm clr command
		gps_com_execute_rpm_clr_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_SPEED_CONVERSION))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_SPEED_CONVERSION,
					strlen(PERIPHERAL_CMD_SET_SPEED_CONVERSION)) == 0)) {
		// speed conversion comand
		gps_com_execute_set_speed_conversion_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_DHL))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_DHL,
					strlen(PERIPHERAL_CMD_SET_DHL)) == 0)) {
		// downhill detection enable comand
		gps_com_execute_set_dhl_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_DRIVER_ALARM))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_DRIVER_ALARM,
					strlen(PERIPHERAL_CMD_SET_DRIVER_ALARM)) == 0)) {
		// non-identified driver alarm enable command
		gps_com_execute_set_driver_alarm_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_ENGINE_CUT))
	        && (strncmp((char*) data_p, PERIPHERAL_CMD_SET_ENGINE_CUT,
	                strlen(PERIPHERAL_CMD_SET_ENGINE_CUT)) == 0)) {
	    // idle engine cut configuration command
	    gps_com_execute_set_engine_cut_cmd(data_p, data_len, uart_num,
	            ack_send_func_p);
	    return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_ADD_DRIVER))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_ADD_DRIVER,
					strlen(PERIPHERAL_CMD_SET_ADD_DRIVER)) == 0)) {
		// add driver comand
		gps_com_execute_set_add_driver_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_DELETE_DRIVER))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_DELETE_DRIVER,
					strlen(PERIPHERAL_CMD_SET_DELETE_DRIVER)) == 0)) {
		// delete driver comand
		gps_com_execute_set_delete_driver_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_DELETE_TPMS))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_DELETE_TPMS,
					strlen(PERIPHERAL_CMD_SET_DELETE_TPMS)) == 0)) {
		// delete tpms comand
		gps_com_execute_set_delete_tpms_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_GEODEL))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_GEODEL,
					strlen(PERIPHERAL_CMD_SET_GEODEL)) == 0)) {
		// speed conversion comand
		gps_com_execute_set_delete_geo_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_DELETE_ACTIVITY_CODE))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_DELETE_ACTIVITY_CODE,
					strlen(PERIPHERAL_CMD_SET_DELETE_ACTIVITY_CODE)) == 0)) {
		// speed conversion comand
		gps_com_execute_set_delete_activity_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_ADD_TPMS))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_ADD_TPMS,
					strlen(PERIPHERAL_CMD_SET_ADD_TPMS)) == 0)) {
		// speed conversion comand
		gps_com_execute_set_add_tpms_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_GEOR))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_GEOR,
					strlen(PERIPHERAL_CMD_SET_GEOR)) == 0)) {
		// speed conversion comand
		gps_com_execute_set_add_geor_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_GEOC))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_GEOC,
					strlen(PERIPHERAL_CMD_SET_GEOC)) == 0)) {
		// speed conversion comand
		gps_com_execute_set_add_geoc_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_IN))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_IN,
					strlen(PERIPHERAL_CMD_SET_IN)) == 0)) {
		// speed conversion comand
		gps_com_execute_set_in_cmd(data_p, data_len, uart_num, ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE,
					strlen(PERIPHERAL_CMD_SET_ADD_ACTIVITY_CODE)) == 0)) {
		// speed conversion comand
		gps_com_execute_set_add_activity_code_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE,
					strlen(PERIPHERAL_CMD_SET_ROLLOVER_ALARM_ANGLE)) == 0)) {
		// rollover alarm angle set command
		gps_com_execute_rollover_set_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_SET_PULSE))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_SET_PULSE,
					strlen(PERIPHERAL_CMD_SET_PULSE)) == 0)) {
		// pulse conversion set command
		gps_com_execute_pulse_set_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_CLR_TURBO))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_CLR_TURBO,
					strlen(PERIPHERAL_CMD_CLR_TURBO)) == 0)) {
		// TURBO clr command
		gps_com_execute_turbo_clr_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_CLR_ODO))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_CLR_ODO,
					strlen(PERIPHERAL_CMD_CLR_ODO)) == 0)) {
		// odo clr command
		gps_com_execute_odo_clr_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_CLR_ENG))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_CLR_ENG,
					strlen(PERIPHERAL_CMD_CLR_ENG)) == 0)) {
		// eng clr command
		gps_com_execute_eng_clr_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= strlen(PERIPHERAL_CMD_REBOOT))
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_REBOOT,
					strlen(PERIPHERAL_CMD_REBOOT)) == 0)) {
		// reboot command
		gps_com_execute_reboot_cmd(data_p, data_len, uart_num, ack_send_func_p);
		return;
	}

	if (uart_num != PERIPHERAL_UART_AUDIO_DEBUG_NUM)
		return;

	// this commands can come only from PERIPH4

	if ((data_len >= PERIPHERAL_CMD_LOG_GPS_LEN)
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_LOG_GPS,
			PERIPHERAL_CMD_LOG_GPS_LEN) == 0)) {
		// log GPS
		audio_debug_com_execute_log_gps_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= PERIPHERAL_CMD_LOG_CAN_LEN)
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_LOG_CAN,
			PERIPHERAL_CMD_LOG_CAN_LEN) == 0)) {
		// log CAN
		audio_debug_com_execute_log_can_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= PERIPHERAL_CMD_LOG_FM_LEN)
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_LOG_FM,
			PERIPHERAL_CMD_LOG_FM_LEN) == 0)) {
		// log FM
		audio_debug_com_execute_log_fm_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= PERIPHERAL_CMD_LOG_TILT_LEN)
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_LOG_TILT,
			PERIPHERAL_CMD_LOG_TILT_LEN) == 0)) {
		// log TILT
		audio_debug_com_execute_log_tilt_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}

	if ((data_len >= PERIPHERAL_CMD_LOG_OFF_LEN)
			&& (strncmp((char*) data_p, PERIPHERAL_CMD_LOG_OFF,
			PERIPHERAL_CMD_LOG_OFF_LEN) == 0)) {
		// log OFF
		audio_debug_com_execute_log_off_cmd(data_p, data_len, uart_num,
				ack_send_func_p);
		return;
	}
}

// Procura GNRMC or GNGGA
static int find_gps_sentence_start(const uint8_t *data_p, size_t data_len) {
    const char *gnrmc = "GNRMC";
    const char *gngga = "GNGGA";
    size_t gnrmc_len = strlen(gnrmc);
    size_t gngga_len = strlen(gngga);

    for (size_t i = 0; i < data_len - (gnrmc_len > gngga_len ? gnrmc_len : gngga_len); ++i) {
        if (strncmp((const char *)&data_p[i], gnrmc, gnrmc_len) == 0 ||
            strncmp((const char *)&data_p[i], gngga, gngga_len) == 0) {
            return i;
        }
    }
    return -1; // Sentence not found
}

//Processa GNGGA para pegar hdop
void gps_com_gngga_processing(uint8_t *data_p, size_t data_len) {
    char delim[] = ",";
    char *end_buff;
    int count = 0;
    float hdop = 0.0f;

    // Acha o inicio
    int start_char = find_gps_sentence_start(data_p, data_len);
    if (start_char == -1) {
        return; // No valid GPS sentence found, exit early
    }

    // Valida o checksum
    if (calc_NMEA_Checksum((data_p + start_char), strlen((char *)(data_p + start_char))) == 0) {
        return;
    }

    // Parse e pega hdop
    char *split_comma = strtok_r((char *)(data_p + start_char), delim, &end_buff);

    while (split_comma != NULL) {
        if (count == 8 && strlen(split_comma) > 0) {
            hdop = atof(split_comma);
            break;
        }
        count++;
        split_comma = strtok_r(NULL, delim, &end_buff);
    }

    // guarda HDOP
    gps_com.hdop = hdop;
}

//Processa GNRMC
void gps_com_gnrmc_processing(uint8_t *data_p, size_t data_len) {
    char delim[] = ",";
    char *end_buff;
    int count = 0;

    int latitudefirstpart = 0;
    float latitudesecondpart = 0;
    float latitude = 0;
    char latitudevariator;

    int longitudefirstpart = 0;
    float longitudesecondpart = 0;
    float longitude = 0;
    char longitudevariator;

    int speed = 0;
    char valid_gps = 'V';

    // Find the start of the GNRMC sentence
    int start_char = find_gps_sentence_start(data_p, data_len);
    if (start_char == -1) {
        return; // No valid GPS sentence found, exit early
    }

    // Validate the checksum
    if (calc_NMEA_Checksum((data_p + start_char), strlen((char *)(data_p + start_char))) == 0) {
        return;
    }

    char *split_comma = strtok_r((char *)(data_p + start_char), delim, &end_buff);

    while (split_comma != NULL) {
        if (count == 2 && strlen(split_comma) >= 1) {
            valid_gps = split_comma[0];
        } else if (count == 3 && strlen(split_comma) >= 5) {
            uint8_t part1[2];
            uint8_t part2[10];
            memset(part2, 0, 10);

            for (int count_part = 0; count_part < strlen(split_comma) && split_comma[count_part] != ','; count_part++) {
                if (count_part == 0 || count_part == 1) {
                    part1[count_part] = split_comma[count_part];
                } else {
                    part2[count_part - 2] = split_comma[count_part];
                }
            }

            latitudefirstpart = atoi((char *)part1);
            latitudesecondpart = atof((char *)part2);

        } else if (count == 4 && strlen(split_comma) >= 1) {
            latitudevariator = split_comma[0];
        } else if (count == 5 && strlen(split_comma) >= 5) {
            uint8_t part1[3];
            uint8_t part2[10];
            memset(part2, 0, 10);

            for (int count_part = 0; count_part < strlen(split_comma) && split_comma[count_part] != ','; count_part++) {
                if (count_part == 0 || count_part == 1 || count_part == 2) {
                    part1[count_part] = split_comma[count_part];
                } else {
                    part2[count_part - 3] = split_comma[count_part];
                }
            }

            longitudefirstpart = atoi((char *)part1);
            longitudesecondpart = atof((char *)part2);

        } else if (count == 6 && strlen(split_comma) >= 1) {
            longitudevariator = split_comma[0];
        } else if (count == 7 && strlen(split_comma) >= 1) {
            speed = atoi(split_comma) * 1.852;
        }

        count++;
        split_comma = strtok_r(NULL, delim, &end_buff);
    }

    // Filter out invalid speeds
    if (speed > 200 || speed < 0) {
        return;
    }

    // Filter out invalid GPS data
    if (valid_gps != 'A') {
        return;
    }

    // Filter out low precision data
    if (gps_com.hdop > 1.5f) {
        return;
    }

    gps_com.speed_gps = speed;

    config_file_t *config_file = config_file_get();
    config_mutex_take(osWaitForever);

    // unidentified driver detection alarm
    if (!driver_is_identified &&
        speed > 10 &&
        gps_com.notification_enabled &&
        keyboard_com_get_ignition_state() &&
        config_file->unidentified_driver_alarm_enabled == 1) {

        gps_com.unidentified_driver_count++;

        if (gps_com.unidentified_driver_count >= 10) {
            // Send audio message
            uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF, 0x06, 0x0F, 0x00, 0x1D, 0x01, 0xEF };
            queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
                              hexBytes_audio_driver, sizeof hexBytes_audio_driver,
                              NULL, 0, 0);

            // Send text message to UART1 with current speed
            char alarm_message[20];  // Increased buffer size to accommodate larger speed values
            snprintf(alarm_message, sizeof(alarm_message), "ALM,113,%d*", speed);
            queue_message_put(gps_com.tx_queue_message_handle, NULL, 0,
                              (uint8_t*)alarm_message, strlen(alarm_message),
                              NULL, 0, 0);

            // Disable further notifications until ignition cycle
            gps_com.notification_enabled = false;
        }
    } else {
        // Reset the count if conditions are not met
        gps_com.unidentified_driver_count = 0;
    }

    if (config_file->speed_type == 0) {  // Using GPS speed
        bool is_wet_condition = (HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_12) == GPIO_PIN_RESET);
        process_speed_control(speed, is_wet_condition);
    }

    // Calculate final latitude and longitude
    latitude = latitudefirstpart + latitudesecondpart / 60;
    if (latitudevariator == 'S') {
        latitude = latitude * -1;
    }
    longitude = longitudefirstpart + longitudesecondpart / 60;
    if (longitudevariator == 'W') {
        longitude = longitude * -1;
    }

    if (config_file->speed_type == 1) {
        speed = can_com_get_speed();
    }

    // Process geofences
    geofence_process(latitude, longitude, speed);

    config_mutex_give();
}

void gps_com_reset_notification_state(void) {
    gps_com.unidentified_driver_count = 0;
    gps_com.notification_enabled = true;
}

osMessageQId gps_com_get_tx_messages_queue(void) {
	return gps_com.tx_queue_message_handle;
}

int gps_com_get_speed_gps() {
	return gps_com.speed_gps;
}

int calc_NMEA_Checksum(char *buf, int cnt) {
	char Character;
	int Checksum = 0;
	int i;              // loop counter
	int asterisk_index = 0;

	//foreach(char Character in sentence)
	for (i = 0; i < cnt; ++i) {
		Character = buf[i];
		switch (Character) {
		case '$':
			// Ignore the dollar sign
			break;
		case '*':
			// Stop processing before the asterisk
			asterisk_index = i;
			i = cnt;
			continue;
		default:
			// Is this the first value for the checksum?
			if (Checksum == 0) {
				// Yes. Set the checksum to the value
				Checksum = Character;
			} else {
				// No. XOR the checksum with this character's value
				Checksum = Checksum ^ Character;
			}
			break;
		}
	}

	int checkstring = 0;
	if (asterisk_index != 0 && asterisk_index + 2 <= cnt) {
		uint8_t string[4];
		memset(string, 0, 4);
		strncpy(string, "0x", 2);

		strncat(string, buf + asterisk_index + 1, 2);

		checkstring = (int) strtol(string, NULL, 16);
	}

	if (checkstring == Checksum)
		return 1;
	else
		return 0;
} // calc_NEMA_Checksum()
