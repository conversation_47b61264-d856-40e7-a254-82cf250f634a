/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _TPMS_COMMUNICATION_H_
#define _TPMS_COMMUNICATION_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
bool tpms_com_init(void);
void tpms_com_time_proc(void);
bool tpms_com_is_idle(void);
void* tpms_com_get_rx_message(bool* is_idle_p);
osMessageQId tpms_com_get_rx_messages_queue(void);
osMessageQId tpms_com_get_tx_messages_queue(void);

void tpms_com_set_counters(uint32_t green_cntr,
                          uint32_t yellow_cntr,
                          uint32_t red_cntr,
                          uint32_t neutral_downhill_cntr
);

void tpms_com_set_turbo_counter(uint32_t pressureOverLimitDuration);

void tpms_com_set_eng_counters(uint64_t movement_timer,
                              uint64_t stopped_timer);

uint8_t tpms_com_get_speed(void);

void tpms_com_emulation_proc();

void tpms_com_save_counters(void);

#endif // _TPMS_COMMUNICATION_H_
