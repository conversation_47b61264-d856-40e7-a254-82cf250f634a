#include "geofence.h"
#include <math.h>
#include <string.h>
#include "../out_ports/out_ports.h"

#define SPEED_THRESHOLD 2  // 2 km/h threshold for triggering alarms
#define CONSECUTIVE_CHECKS 2  // Number of consecutive checks before triggering an alarm

volatile bool geofence_config_changed = false;

// Define the global variables
RectangularGeofence rect_geofences[MAX_GEOFENCES];
CircularGeofence circ_geofences[MAX_GEOFENCES];
uint8_t rect_geofence_count = 0;
uint8_t circ_geofence_count = 0;

// Add static variables for speed management
static uint8_t current_speed = 0;
static bool valid_speed_received = false;

// Add variables for state tracking
static uint8_t consecutive_checks[MAX_GEOFENCES * 2] = {0};  // For both rectangular and circular geofences

// Declare external functions and variables
extern void queue_message_put(void* queue, void* data, size_t size, void* payload, size_t payload_size, void* footer, size_t footer_size, uint32_t timeout);
extern void* gps_com_get_tx_messages_queue(void);

static float haversine_distance(float lat1, float lon1, float lat2, float lon2) {
    float dlat = (lat2 - lat1) * M_PI / 180.0;
    float dlon = (lon2 - lon1) * M_PI / 180.0;
    float a = sin(dlat/2) * sin(dlat/2) + cos(lat1 * M_PI / 180.0) * cos(lat2 * M_PI / 180.0) * sin(dlon/2) * sin(dlon/2);
    float c = 2 * atan2(sqrt(a), sqrt(1-a));
    return 6371000 * c; // Earth's radius in meters
}

void geofence_init(void) {
    // Initialize geofences from config_file
    config_file_t *config_file = config_file_get();

    rect_geofence_count = 0;
    circ_geofence_count = 0;

    // Initialize speed-related variables
    current_speed = 0;
    valid_speed_received = false;

    // Initialize consecutive checks
    memset(consecutive_checks, 0, sizeof(consecutive_checks));

    for (int i = 0; i < MAX_GEOFENCES; i++) {
        if (strlen(config_file->array_geofence_rectangular[i].code) > 0) {
            rect_geofences[rect_geofence_count].in_out = config_file->array_geofence_rectangular[i].in_out;
            rect_geofences[rect_geofence_count].speed_max = config_file->array_geofence_rectangular[i].speed_max;
            rect_geofences[rect_geofence_count].upper_left.latitude = config_file->array_geofence_rectangular[i].upper_left_point_lat;
            rect_geofences[rect_geofence_count].upper_left.longitude = config_file->array_geofence_rectangular[i].upper_left_point_long;
            rect_geofences[rect_geofence_count].lower_right.latitude = config_file->array_geofence_rectangular[i].lower_right_point_lat;
            rect_geofences[rect_geofence_count].lower_right.longitude = config_file->array_geofence_rectangular[i].lower_right_point_long;
            strncpy(rect_geofences[rect_geofence_count].code, config_file->array_geofence_rectangular[i].code, 4);
            rect_geofences[rect_geofence_count].is_active = false;
            rect_geofences[rect_geofence_count].speed_alarm_active = false;
            rect_geofence_count++;
        }

        if (strlen(config_file->array_geofence_circular[i].code) > 0) {
            circ_geofences[circ_geofence_count].in_out = config_file->array_geofence_circular[i].in_out;
            circ_geofences[circ_geofence_count].speed_max = config_file->array_geofence_circular[i].speed_max;
            circ_geofences[circ_geofence_count].center.latitude = config_file->array_geofence_circular[i].central_point_lat;
            circ_geofences[circ_geofence_count].center.longitude = config_file->array_geofence_circular[i].central_point_long;
            circ_geofences[circ_geofence_count].radius = config_file->array_geofence_circular[i].radius;
            strncpy(circ_geofences[circ_geofence_count].code, config_file->array_geofence_circular[i].code, 4);
            circ_geofences[circ_geofence_count].is_active = false;
            circ_geofences[circ_geofence_count].speed_alarm_active = false;
            circ_geofence_count++;
        }
    }
}

bool geofence_check_rectangular(float lat, float lon, const RectangularGeofence* fence) {
    return (lat <= fence->upper_left.latitude && lat >= fence->lower_right.latitude &&
            lon >= fence->upper_left.longitude && lon <= fence->lower_right.longitude);
}

bool geofence_check_circular(float lat, float lon, const CircularGeofence* fence) {
    float distance = haversine_distance(lat, lon, fence->center.latitude, fence->center.longitude);
    return distance <= fence->radius;
}

void geofence_trigger_alarm(const char* code, bool is_entry) {
    char alarm_msg[20];
    snprintf(alarm_msg, sizeof(alarm_msg), "ALM,%s,%s*", is_entry ? "104" : "105", code);
    queue_message_put(gps_com_get_tx_messages_queue(), NULL, 0, alarm_msg, strlen(alarm_msg), NULL, 0, 0);
}

/*void geofence_trigger_speed_alarm(const char* code, uint8_t speed) {
    char alarm_msg[25];
    snprintf(alarm_msg, sizeof(alarm_msg), "ALM,103,%d|%s*", speed, code);
    queue_message_put(gps_com_get_tx_messages_queue(), NULL, 0, alarm_msg, strlen(alarm_msg), NULL, 0, 0);
}*/

void geofence_trigger_speed_alarm(const char*, uint8_t speed) {
    char alarm_msg[20];
    snprintf(alarm_msg, sizeof(alarm_msg), "ALM,103,%d*", speed);
    queue_message_put(gps_com_get_tx_messages_queue(), NULL, 0, alarm_msg, strlen(alarm_msg), NULL, 0, 0);
}

void geofence_process(float latitude, float longitude, uint8_t speed) {
    if (geofence_config_changed) {
        geofence_init();
        geofence_config_changed = false;
    }

    // Update speed and valid_speed_received flag
    if (speed != 255) {  // Assuming 255 is an invalid speed value
        current_speed = speed;
        valid_speed_received = true;
    }

    bool buzzer_should_be_on = false;

    for (int i = 0; i < rect_geofence_count; i++) {
        bool is_inside = geofence_check_rectangular(latitude, longitude, &rect_geofences[i]);
        bool was_inside = rect_geofences[i].is_active;

        if (is_inside != was_inside && valid_speed_received && current_speed > SPEED_THRESHOLD) {
            consecutive_checks[i]++;
            if (consecutive_checks[i] >= CONSECUTIVE_CHECKS) {
                rect_geofences[i].is_active = is_inside;
                if ((rect_geofences[i].in_out == 1 && is_inside) || (rect_geofences[i].in_out == 0 && !is_inside)) {
                    geofence_trigger_alarm(rect_geofences[i].code, is_inside);
                }
                consecutive_checks[i] = 0;
            }
        } else {
            consecutive_checks[i] = 0;
        }

        if (valid_speed_received && current_speed > SPEED_THRESHOLD &&
            ((rect_geofences[i].in_out == 1 && is_inside) || (rect_geofences[i].in_out == 0 && !is_inside))) {
            if (current_speed > rect_geofences[i].speed_max) {
                if (!rect_geofences[i].speed_alarm_active) {
                    rect_geofences[i].speed_alarm_active = true;
                    geofence_trigger_speed_alarm(rect_geofences[i].code, current_speed);
                }
                buzzer_should_be_on = true;
            } else {
                rect_geofences[i].speed_alarm_active = false;
            }
        } else {
            rect_geofences[i].speed_alarm_active = false;
        }
    }

    for (int i = 0; i < circ_geofence_count; i++) {
        bool is_inside = geofence_check_circular(latitude, longitude, &circ_geofences[i]);
        bool was_inside = circ_geofences[i].is_active;

        if (is_inside != was_inside && valid_speed_received && current_speed > SPEED_THRESHOLD) {
            consecutive_checks[rect_geofence_count + i]++;
            if (consecutive_checks[rect_geofence_count + i] >= CONSECUTIVE_CHECKS) {
                circ_geofences[i].is_active = is_inside;
                if ((circ_geofences[i].in_out == 1 && is_inside) || (circ_geofences[i].in_out == 0 && !is_inside)) {
                    geofence_trigger_alarm(circ_geofences[i].code, is_inside);
                }
                consecutive_checks[rect_geofence_count + i] = 0;
            }
        } else {
            consecutive_checks[rect_geofence_count + i] = 0;
        }

        if (valid_speed_received && current_speed > SPEED_THRESHOLD &&
            ((circ_geofences[i].in_out == 1 && is_inside) || (circ_geofences[i].in_out == 0 && !is_inside))) {
            if (current_speed > circ_geofences[i].speed_max) {
                if (!circ_geofences[i].speed_alarm_active) {
                    circ_geofences[i].speed_alarm_active = true;
                    geofence_trigger_speed_alarm(circ_geofences[i].code, current_speed);
                }
                buzzer_should_be_on = true;
            } else {
                circ_geofences[i].speed_alarm_active = false;
            }
        } else {
            circ_geofences[i].speed_alarm_active = false;
        }
    }

    set_buzzer(buzzer_should_be_on);
}
