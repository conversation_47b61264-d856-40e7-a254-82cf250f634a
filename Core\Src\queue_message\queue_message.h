/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _QUEUE_MESSAGE_H_
#define _QUEUE_MESSAGE_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/
typedef struct {
  size_t    size;
  uint8_t   buff[1];
} queue_message_t;

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
bool  queue_message_put(osMessageQId queue_id,
                        void* header_p, size_t header_size,
                        void* message_p, size_t message_size,
                        void* footer_p, size_t footer_size,
                        uint32_t timeout);
void* queue_message_get(osMessageQId queue_id, uint32_t timeout);
void  queue_message_clr(osMessageQId queue_id);
void queue_message_free(void* message);

#endif // _QUEUE_MESSAGE_H_
