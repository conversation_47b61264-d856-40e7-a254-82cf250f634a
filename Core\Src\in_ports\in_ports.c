/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "in_ports.h"
#include "../config/config.h"
#include "../can_com/can_com.h"
#include "../gps_com/gps_com.h"
#include "../queue_message/queue_message.h"
#include "../watchdog/watchdog.h"
/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Private Types
 ****************************************************************************/

typedef struct {
	osMessageQId rx_queue_message_handle; // rx messages(received from can and processed)
	osThreadId in_port_handle;

	int in3_on;
	int in4_on;
	int in5_on;
	int in6_on;
	int in7_on;
	int in8_on;
	int in9_on;
	int in10_on;

	int in3_delay_time;
	int in4_delay_time;
	int in5_delay_time;
	int in6_delay_time;
	int in7_delay_time;
	int in8_delay_time;
	int in9_delay_time;
	int in10_delay_time;

	int in3_debonce_time;
	int in4_debonce_time;
	int in5_debonce_time;
	int in6_debonce_time;
	int in7_debonce_time;
	int in8_debonce_time;
	int in9_debonce_time;
	int in10_debonce_time;

} in_ports_t;

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Private Data
 ****************************************************************************/
static in_ports_t in_ports;
static uint8_t alarme[10];
/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Public Functions
 ****************************************************************************/

void* in_ports_get_rx_message(void) {

	__disable_irq();

	queue_message_t *message_p = (queue_message_t*) queue_message_get(
			in_ports.rx_queue_message_handle, 0);

	__enable_irq();

	return (void*) message_p;
}

static void in_port_thread(const void *arg) {

	for (;;) {

		if (strlen(alarme) == 10) {
			queue_message_put(in_ports.rx_queue_message_handle, NULL, 0, alarme,
					10, NULL, 0, 0);
			memset(alarme, 0, 10);
			osDelay(100);
		}
		// get mail from CAN RX mail queue

		watchdog_reset();

	} // thread loop

}

bool in_ports_init(void) {
	memset(alarme, 0, 10);
	in_ports.in3_on = 0;
	in_ports.in4_on = 0;
	in_ports.in5_on = 0;
	in_ports.in6_on = 0;
	in_ports.in7_on = 0;
	in_ports.in8_on = 0;
	in_ports.in9_on = 0;
	in_ports.in10_on = 0;

	in_ports.in3_delay_time = 0;
	in_ports.in4_delay_time = 0;
	in_ports.in5_delay_time = 0;
	in_ports.in6_delay_time = 0;
	in_ports.in7_delay_time = 0;
	in_ports.in8_delay_time = 0;
	in_ports.in9_delay_time = 0;
	in_ports.in10_delay_time = 0;

	in_ports.in3_debonce_time = 0;
	in_ports.in4_debonce_time = 0;
	in_ports.in5_debonce_time = 0;
	in_ports.in6_debonce_time = 0;
	in_ports.in7_debonce_time = 0;
	in_ports.in8_debonce_time = 0;
	in_ports.in9_debonce_time = 0;
	in_ports.in10_debonce_time = 0;

// create rx message queue
	osMessageQDef(rx_message_queue, 5, 0);
	in_ports.rx_queue_message_handle = osMessageCreate(
			osMessageQ(rx_message_queue),
			NULL);
	if (in_ports.rx_queue_message_handle == NULL) {
		Error_Handler();
		return false;
	}

	osThreadDef(in_port_task, in_port_thread, osPriorityNormal, 0,
			configMINIMAL_STACK_SIZE * 2);
	in_ports.in_port_handle = osThreadCreate(osThread(in_port_task), NULL);
	if (in_ports.in_port_handle == NULL) {
		Error_Handler();
		return false;
	}

}

void time_in_ports_proc() {
	config_file_t *config_file = config_file_get();
	config_mutex_take(osWaitForever);

	//in4
	if (in_ports.in4_debonce_time > 0
			&& config_file->in4_debounce_time > in_ports.in4_debonce_time) {
		in_ports.in4_debonce_time++;
	} else {
		in_ports.in4_debonce_time = 0;
		if (config_file->in4_enabled == 1
				&& HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_12) == GPIO_PIN_RESET) {
			if (in_ports.in4_on == 0) {
				if (config_file->in4_delay_time > in_ports.in4_delay_time) {
					in_ports.in4_delay_time++;
				} else {
					in_ports.in4_on = 1;
					can_com_set_in_stat(4, 1);

					if (config_file->in4_notify_when_enabled == 1) {
						memset(alarme, 0, sizeof alarme);
						strncpy(alarme, "ALM,124,1*", 10);
					}

				}
			}
		} else if (config_file->in4_enabled == 1 && in_ports.in4_on == 1) {
			in_ports.in4_debonce_time = 1;
			in_ports.in4_delay_time = 0;
			in_ports.in4_on = 0;
			can_com_set_in_stat(4, 0);
			if (config_file->in4_notify_when_disabled == 1) {
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,124,0*", 10);
			}
		} else {
			in_ports.in4_debonce_time = 0;
			in_ports.in4_delay_time = 0;
			in_ports.in4_on = 0;
			can_com_set_in_stat(4, 0);
		}
	}

	//in5
	if (in_ports.in5_debonce_time > 0
			&& config_file->in5_debounce_time > in_ports.in5_debonce_time) {
		in_ports.in5_debonce_time++;
	} else {
		in_ports.in5_debonce_time = 0;

		if (config_file->in5_enabled == 1
				&& HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_3) == GPIO_PIN_RESET) {
			if (in_ports.in5_on == 0) {
				if (config_file->in5_delay_time > in_ports.in5_delay_time) {
					in_ports.in5_delay_time++;
				} else {
					in_ports.in5_on = 1;
					can_com_set_in_stat(5, 1);

					if (config_file->in5_notify_when_enabled == 1) {
						memset(alarme, 0, sizeof alarme);
						strncpy(alarme, "ALM,125,1*", 10);
					}
				}
			}
		} else if (config_file->in5_enabled == 1 && in_ports.in5_on == 1) {
			in_ports.in5_debonce_time = 1;
			in_ports.in5_delay_time = 0;
			in_ports.in5_on = 0;
			can_com_set_in_stat(5, 0);
			if (config_file->in5_notify_when_disabled == 1) {
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,125,0*", 10);
			}
		} else {
			in_ports.in5_debonce_time = 0;
			in_ports.in5_delay_time = 0;
			in_ports.in5_on = 0;
			can_com_set_in_stat(5, 0);
		}
	}

	//in6
	if (in_ports.in6_debonce_time > 0
			&& config_file->in6_debounce_time > in_ports.in6_debonce_time) {
		in_ports.in6_debonce_time++;
	} else {
		in_ports.in6_debonce_time = 0;

		if (config_file->in6_enabled == 1
				&& HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_2) == GPIO_PIN_RESET) {
			if (in_ports.in6_on == 0) {
				if (config_file->in6_delay_time > in_ports.in6_delay_time) {
					in_ports.in6_delay_time++;
				} else {
					in_ports.in6_on = 1;
					can_com_set_in_stat(6, 1);

					if (config_file->in6_notify_when_enabled == 1) {
						memset(alarme, 0, sizeof alarme);
						strncpy(alarme, "ALM,126,1*", 10);
					}

				}
			}
		} else if (config_file->in6_enabled == 1 && in_ports.in6_on == 1) {
			in_ports.in6_debonce_time = 1;
			in_ports.in6_delay_time = 0;
			in_ports.in6_on = 0;
			can_com_set_in_stat(6, 0);

			if (config_file->in6_notify_when_disabled == 1) {
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,126,0*", 10);
			}
		} else {
			in_ports.in6_debonce_time = 0;
			in_ports.in6_delay_time = 0;
			in_ports.in6_on = 0;
			can_com_set_in_stat(6, 0);
		}
	}

	//in7
	if (in_ports.in7_debonce_time > 0
			&& config_file->in7_debounce_time > in_ports.in7_debonce_time) {
		in_ports.in7_debonce_time++;
	} else {
		in_ports.in7_debonce_time = 0;

		if (config_file->in7_enabled == 1
				&& HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_4) == GPIO_PIN_RESET) {
			if (in_ports.in7_on == 0) {
				if (config_file->in7_delay_time > in_ports.in7_delay_time) {
					in_ports.in7_delay_time++;
				} else {
					in_ports.in7_on = 1;
					can_com_set_in_stat(7, 1);

					if (config_file->in7_notify_when_enabled == 1) {
						memset(alarme, 0, sizeof alarme);
						strncpy(alarme, "ALM,127,1*", 10);
					}

				}
			}
		} else if (config_file->in7_enabled == 1 && in_ports.in7_on == 1) {
			in_ports.in7_debonce_time = 1;
			in_ports.in7_delay_time = 0;
			in_ports.in7_on = 0;
			can_com_set_in_stat(7, 0);
			if (config_file->in7_notify_when_disabled == 1) {
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,127,0*", 10);
			}
		} else {
			in_ports.in7_debonce_time = 0;
			in_ports.in7_delay_time = 0;
			in_ports.in7_on = 0;
			can_com_set_in_stat(7, 0);
		}
	}

	//in8
	if (in_ports.in8_debonce_time > 0
			&& config_file->in8_debounce_time > in_ports.in8_debonce_time) {
		in_ports.in8_debonce_time++;
	} else {
		in_ports.in8_debonce_time = 0;

		if (config_file->in8_enabled == 1
				&& HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_5) == GPIO_PIN_RESET) {
			if (in_ports.in8_on == 0) {
				if (config_file->in8_delay_time > in_ports.in8_delay_time) {
					in_ports.in8_delay_time++;
				} else {
					in_ports.in8_on = 1;
					can_com_set_in_stat(8, 1);

					if (config_file->in8_notify_when_enabled == 1) {
						memset(alarme, 0, sizeof alarme);
						strncpy(alarme, "ALM,128,1*", 10);
					}

				}
			}
		} else if (config_file->in8_enabled == 1 && in_ports.in8_on == 1) {
			in_ports.in8_debonce_time = 1;
			in_ports.in8_delay_time = 0;
			in_ports.in8_on = 0;
			can_com_set_in_stat(8, 0);
			if (config_file->in8_notify_when_disabled == 1) {
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,128,0*", 10);
			}
		} else {
			in_ports.in8_debonce_time = 0;
			in_ports.in8_delay_time = 0;
			in_ports.in8_on = 0;
			can_com_set_in_stat(8, 0);
		}
	}

	//in9
	if (in_ports.in9_debonce_time > 0
			&& config_file->in9_debounce_time > in_ports.in9_debonce_time) {
		in_ports.in9_debonce_time++;
	} else {
		in_ports.in9_debonce_time = 0;

		if (config_file->in9_enabled == 1
				&& HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_8) == GPIO_PIN_RESET) {
			if (in_ports.in9_on == 0) {
				if (config_file->in9_delay_time > in_ports.in9_delay_time) {
					in_ports.in9_delay_time++;
				} else {
					in_ports.in9_on = 1;
					can_com_set_in_stat(9, 1);

					if (config_file->in9_notify_when_enabled == 1) {
						memset(alarme, 0, sizeof alarme);
						strncpy(alarme, "ALM,129,1*", 10);
					}

				}
			}
		} else if (config_file->in9_enabled == 1 && in_ports.in9_on == 1) {
			in_ports.in9_debonce_time = 1;
			in_ports.in9_delay_time = 0;
			in_ports.in9_on = 0;
			can_com_set_in_stat(9, 0);
			if (config_file->in9_notify_when_disabled == 1) {
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,129,0*", 10);
			}
		} else {
			in_ports.in9_debonce_time = 0;
			in_ports.in9_delay_time = 0;
			in_ports.in9_on = 0;
			can_com_set_in_stat(9, 0);
		}
	}

	//in10
	if (in_ports.in10_debonce_time > 0
			&& config_file->in10_debounce_time > in_ports.in10_debonce_time) {
		in_ports.in10_debonce_time++;
	} else {
		in_ports.in10_debonce_time = 0;

		if (config_file->in10_enabled == 1
				&& HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_9) == GPIO_PIN_RESET) {
			if (in_ports.in10_on == 0) {
				if (config_file->in10_delay_time > in_ports.in10_delay_time) {
					in_ports.in10_delay_time++;
				} else {
					in_ports.in10_on = 1;
					can_com_set_in_stat(10, 1);

					if (config_file->in10_notify_when_enabled == 1) {
						memset(alarme, 0, sizeof alarme);
						strncpy(alarme, "ALM,130,1*", 10);
					}
				}
			}
		} else if (config_file->in10_enabled == 1 && in_ports.in10_on == 1) {
			in_ports.in10_debonce_time = 1;
			in_ports.in10_delay_time = 0;
			in_ports.in10_on = 0;
			can_com_set_in_stat(10, 0);
			if (config_file->in10_notify_when_disabled == 1) {
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,130,0*", 10);
			}
		} else {
			in_ports.in10_debonce_time = 0;
			in_ports.in10_delay_time = 0;
			in_ports.in10_on = 0;
			can_com_set_in_stat(10, 0);
		}
	}

	config_mutex_give();

}
