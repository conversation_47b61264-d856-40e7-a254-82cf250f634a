/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "audio_debug_uart.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define UART_NUMBER                 UART4
#define UART_BAUDRATE               9600

#define AUDIO_DEBUG_IRQHandler         UART4_IRQHandler

#define UART_CLK_ENABLE()           __HAL_RCC_UART4_CLK_ENABLE()
#define UART_CLK_DISABLE()          __HAL_RCC_UART4_CLK_DISABLE()

#define UART_GPIO_CLK_ENABLE()      __HAL_RCC_GPIOC_CLK_ENABLE()

#define UART_DMA_CLK_ENABLE()       __HAL_RCC_DMA2_CLK_ENABLE()

/****************************************************************************
 * Private Types
 ****************************************************************************/
/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void);

/****************************************************************************
 * Private Data
 ****************************************************************************/
UART_HandleTypeDef huart4;

//ALTERADO
//static DMA_HandleTypeDef hdma_tx;
static void (*tx_cpl_cb_p)(void) = NULL;

//ALTERADO
//static DMA_HandleTypeDef hdma_rx;
static void (*rx_cpl_cb_p)(uint8_t* data_p, size_t data_len) = NULL;
static uint8_t rx_data[AUDIO_DEBUG_UART_RX_BUFF_SIZE];

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void)
{
  __disable_irq();

  HAL_StatusTypeDef status = HAL_UARTEx_ReceiveToIdle_DMA(&huart4, rx_data, sizeof(rx_data));

  __enable_irq();

  return status;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
UART_HandleTypeDef* audio_debug_uart_get(void)
{
  return &huart4;
}

bool audio_debug_uart_init(void (*rx_cpl_cb)(uint8_t* data_p, size_t data_len), bool high_speed)
{
   huart4.Instance = UART4;

   if (high_speed == true)
	   huart4.Init.BaudRate = 115200;
   else
	   huart4.Init.BaudRate = 9600;

   huart4.Init.WordLength = UART_WORDLENGTH_8B;
   huart4.Init.StopBits = UART_STOPBITS_1;
   huart4.Init.Parity = UART_PARITY_NONE;
   huart4.Init.Mode = UART_MODE_TX_RX;
   huart4.Init.HwFlowCtl = UART_HWCONTROL_NONE;
   huart4.Init.OverSampling = UART_OVERSAMPLING_16;
   if (HAL_UART_Init(&huart4) != HAL_OK)
   {
     Error_Handler();
   }

  // start rx
  rx_cpl_cb_p = rx_cpl_cb;
  if (uart_start_rx() != HAL_OK) {
    return false;
  }

  return true;
}

bool audio_debug_uart_reinit()
{
  // deinit usart
  if (HAL_UART_DeInit(&huart4) != HAL_OK) {
    return false;
  }

   huart4.Instance = UART4;
   huart4.Init.BaudRate = 9600;
   huart4.Init.WordLength = UART_WORDLENGTH_8B;
   huart4.Init.StopBits = UART_STOPBITS_1;
   huart4.Init.Parity = UART_PARITY_NONE;
   huart4.Init.Mode = UART_MODE_TX_RX;
   huart4.Init.HwFlowCtl = UART_HWCONTROL_NONE;
   huart4.Init.OverSampling = UART_OVERSAMPLING_16;
   if (HAL_UART_Init(&huart4) != HAL_OK)
   {
     Error_Handler();
   }

   UART4->CR1&= ~USART_CR1_TE_Msk;	// disable TX to force IDLE frame next time

  // start rx
  if (uart_start_rx() != HAL_OK) {
    return false;
  }

  return true;
}

bool audio_debug_uart_send(uint8_t* data_p, size_t data_len, void (*tx_cpl_cb)(void))
{
  if ((data_p == NULL) || (data_len == 0)) {
    return false;
  }
  tx_cpl_cb_p = tx_cpl_cb;

  if (HAL_UART_Transmit(&huart4, data_p, data_len, osWaitForever) != HAL_OK) {
    tx_cpl_cb_p = NULL;
    return false;
  }
  __enable_irq();
  return true;
}

bool audio_debug_uart_is_rx_started(void)
{
  if ((huart4.RxState == HAL_UART_STATE_BUSY_RX) &&
      (huart4.hdmarx->Instance->NDTR < AUDIO_DEBUG_UART_RX_BUFF_SIZE)) {
    return true;
  }
  return false;
}

void audio_debug_uart_rx_cpl_cb(void)
{
  if (rx_cpl_cb_p != NULL) {
    // calculate rx len
    size_t rx_len = huart4.hdmarx->Instance->NDTR;
    if (rx_len > AUDIO_DEBUG_UART_RX_BUFF_SIZE) {
      rx_len = AUDIO_DEBUG_UART_RX_BUFF_SIZE;
    }
    rx_len = AUDIO_DEBUG_UART_RX_BUFF_SIZE - rx_len;

    // notify about new data
    if (rx_len > 0) { // check to prevent execute cp second time
      rx_cpl_cb_p(rx_data, rx_len);
    }
  }

  // start rx again
  uart_start_rx();
}

void audio_debug_uart_tx_cpl_cb(void)
{
  __disable_irq();

  if (tx_cpl_cb_p != NULL) {
    tx_cpl_cb_p();
    tx_cpl_cb_p = NULL;
  }

  __enable_irq();
}

void audio_debug_uart_err_cb(void)
{
  // abort
  __HAL_UART_FLUSH_DRREGISTER(&huart4);
  HAL_UART_Abort(&huart4);

  // do tx_cpl_cb to unlock any waiting tasks
  audio_debug_uart_tx_cpl_cb();

  // start rx again
  uart_start_rx();
}


void AUDIO_DEBUG_IRQHandler(void) {
	// check for idle irq
	bool is_idle = false;
	if (__HAL_UART_GET_FLAG(&huart4, UART_FLAG_IDLE)) {
		is_idle = true;
	}

	// irq processing
	HAL_UART_IRQHandler(&huart4);

	// process idle irq
	if (is_idle) {
		__HAL_UART_CLEAR_IDLEFLAG(&huart4);
		HAL_UART_AbortReceive(&huart4);
		HAL_UART_RxCpltCallback(&huart4);
	}
}
