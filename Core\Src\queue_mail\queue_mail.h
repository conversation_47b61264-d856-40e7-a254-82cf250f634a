/****************************************************************************
 *   Copyright (C) 2019.12.02. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _QUEUE_MAIL_H_
#define _QUEUE_MAIL_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/
typedef struct {
  size_t    size;
  uint8_t   buff[1];
} queue_mail_t;

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
bool  queue_mail_put(osMailQId queue_id, void* mail_p, size_t mail_size, uint32_t timeout);
void* queue_mail_get(osMailQId queue_id, uint32_t timeout);
void  queue_mail_clr(osMailQId queue_id);
void  queue_mail_free(osMailQId queue_id, void* message);

#endif // _QUEUE_MAIL_H_
