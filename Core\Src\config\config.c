/****************************************************************************
 *   Copyright (C) 2020.01.22. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "config.h"
#include "../can_com/can_com.h"
#include "../rpm/rpm.h"
#include "../watchdog/watchdog.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static void config_load_or_set_to_default(void);
config_file_t* config_file_get(void);
/****************************************************************************
 * Private Data
 ****************************************************************************/
static osMutexId config_mutex_hanle = NULL;
static config_file_t config_file;

/****************************************************************************
 * Public Data
 ****************************************************************************/
extern const volatile unsigned int __ICFEDIT_region_config_start__;

/****************************************************************************
 * Private Functions
 ****************************************************************************/
static void config_load_or_set_to_default(void) {

	load_config();

}

bool save_config(void) {

	if (CSP_QUADSPI_Init() != HAL_OK) {
		Error_Handler();
	}

	if (CSP_QSPI_EraseSector((uint8_t*) &config_file,
			(uint8_t*) &config_file + sizeof config_file) != HAL_OK) {
		Error_Handler();
	}

	if (CSP_QSPI_WriteMemory((uint8_t*) &config_file, 0, sizeof config_file)
			!= HAL_OK) {
		Error_Handler();
	}

	if (CSP_QSPI_EnableMemoryMappedMode() != HAL_OK) {
		Error_Handler();
	}

	return true;
}

bool load_config(void) {

	if (CSP_QUADSPI_Init() != HAL_OK) {
		Error_Handler();
	}

	if (CSP_QSPI_EnableMemoryMappedMode() != HAL_OK) {
		Error_Handler();
	}

	config_file_t *config_file_flash = (config_file_t*) 0x90000000;
	config_file = *config_file_flash;

	if (config_file.loaded == false) {

		memset(&config_file, 0, sizeof config_file);
		memset(config_file.array_driver_id, 0,
				sizeof config_file.array_driver_id);
		memset(config_file.array_activity_code, 0,
				sizeof config_file.array_activity_code);
		memset(config_file.array_tpms, 0, sizeof config_file.array_tpms);
		memset(config_file.array_geofence_circular, 0,
				sizeof config_file.array_geofence_circular);
		memset(config_file.array_geofence_rectangular, 0,
				sizeof config_file.array_geofence_rectangular);

		//Aqui setaremos os valores padrão iniciais
		config_file.movement_timer = 0;
		config_file.stopped_timer = 0;
		config_file.odometer_count = 0;
		config_file.rpm_green_count = 0;
		config_file.rpm_yellow_count = 0;
		config_file.rpm_red_count = 0;
		config_file.downhill_count = 0;
		config_file.turbo_count = 0;

		config_file.loaded = true;
		config_file.rpm_green_min = 500;
		config_file.rpm_green_max = 2100;
		config_file.rpm_yellow_min = 2101;
		config_file.rpm_yellow_max = 2700;
		config_file.rpm_red_min = 2701;
		config_file.rpm_red_max = 4500;

		config_file.downhill_rpm_max = 750;
		config_file.downhill_speed_min = 40;
		config_file.downhill_seconds_min = 5;

		config_file.rpm_max = 2400;

		config_file.temperature_engine_max = 102;
		config_file.turbo_pressure_max = 268;

		config_file.uart2_speed = 115200;
		config_file.uart2_bits = 8;
		config_file.uart2_stop_bit = 1;
		config_file.uart2_flow_control[1] = 'N';

		config_file.uart3_speed = 115200;
		config_file.uart3_bits = 8;
		config_file.uart3_stop_bit = 1;
		config_file.uart3_flow_control[1] = 'N';

		config_file.uart4_speed = 9600;
		config_file.uart4_bits = 8;
		config_file.uart4_stop_bit = 1;
		config_file.uart4_flow_control[1] = 'N';

		config_file.uart5_speed = 9600;
		config_file.uart5_bits = 8;
		config_file.uart5_stop_bit = 1;
		config_file.uart5_flow_control[1] = 'N';

		config_file.uart6_speed = 115200;
		config_file.uart6_bits = 8;
		config_file.uart6_stop_bit = 1;
		config_file.uart6_flow_control[1] = 'N';

		config_file.pulse_enabled = 0;
		config_file.pulse_rpm_factor = 0;
		config_file.pulse_speed_factor = 0;

		config_file.speed_type = 1; //0- GPS, 1- CANBUS

		config_file.speed_1_speed_beep = 85;
		config_file.speed_1_speed_alarms = 90;
		config_file.speed_1_seconds_min = 0;
		config_file.speed_1_activate_out2 = 0;

		config_file.speed_2_speed_beep = 67;
		config_file.speed_2_speed_alarms = 70;
		config_file.speed_2_seconds_min = 0;
		config_file.speed_2_activate_out2 = 0;

		config_file.downhill_enabled = 1;

		config_file.fuel_threshold_out = 10;
		config_file.fuel_threshold_in = 20;

		config_file.roll_max = 40;

		config_file.in3_enabled = 1;
		config_file.in3_notify_when_enabled = 0;
		config_file.in3_notify_when_disabled = 0;
		config_file.in3_delay_time = 0;
		config_file.in3_debounce_time = 0;

		config_file.in4_enabled = 1;
		config_file.in4_notify_when_enabled = 1;
		config_file.in4_notify_when_disabled = 1;
		config_file.in4_delay_time = 30;
		config_file.in4_debounce_time = 0;

		config_file.in5_enabled = 1;
		config_file.in5_notify_when_enabled = 1;
		config_file.in5_notify_when_disabled = 1;
		config_file.in5_delay_time = 0;
		config_file.in5_debounce_time = 0;

		config_file.in6_enabled = 1;
		config_file.in6_notify_when_enabled = 1;
		config_file.in6_notify_when_disabled = 1;
		config_file.in6_delay_time = 0;
		config_file.in6_debounce_time = 0;

		config_file.in7_enabled = 1;
		config_file.in7_notify_when_enabled = 1;
		config_file.in7_notify_when_disabled = 1;
		config_file.in7_delay_time = 0;
		config_file.in7_debounce_time = 0;

		config_file.in8_enabled = 1;
		config_file.in8_notify_when_enabled = 1;
		config_file.in8_notify_when_disabled = 1;
		config_file.in8_delay_time = 0;
		config_file.in8_debounce_time = 0;

		config_file.in9_enabled = 1;
		config_file.in9_notify_when_enabled = 1;
		config_file.in9_notify_when_disabled = 1;
		config_file.in9_delay_time = 0;
		config_file.in9_debounce_time = 0;

		config_file.in10_enabled = 1;
		config_file.in10_notify_when_enabled = 1;
		config_file.in10_notify_when_disabled = 1;
		config_file.in10_delay_time = 0;
		config_file.in10_debounce_time = 0;

		config_file.out2_enabled = 0;
		config_file.out2_time = 0;

		config_file.out3_enabled = 0;
		config_file.out3_time = 0;

		config_file.out4_enabled = 0;
		config_file.out4_time = 0;

		config_file.out4_enabled = 0;
		config_file.out4_time = 0;

		config_file.keyboard_type = 0;

		strncpy(config_file.array_activity_code[0].activity_code, "11", 2);
		strncpy(config_file.array_activity_code[1].activity_code, "12", 2);
		strncpy(config_file.array_activity_code[2].activity_code, "19", 2);
		strncpy(config_file.array_activity_code[3].activity_code, "22", 2);
		strncpy(config_file.array_activity_code[4].activity_code, "23", 2);
		strncpy(config_file.array_activity_code[5].activity_code, "34", 2);
		strncpy(config_file.array_activity_code[6].activity_code, "42", 2);
		strncpy(config_file.array_activity_code[7].activity_code, "47", 2);
		strncpy(config_file.array_activity_code[8].activity_code, "48", 2);
		strncpy(config_file.array_activity_code[9].activity_code, "49", 2);
		strncpy(config_file.array_activity_code[10].activity_code, "56", 2);
		strncpy(config_file.array_activity_code[11].activity_code, "61", 2);
		strncpy(config_file.array_activity_code[12].activity_code, "65", 2);
		strncpy(config_file.array_activity_code[13].activity_code, "78", 2);
		strncpy(config_file.array_activity_code[14].activity_code, "79", 2);
		strncpy(config_file.array_activity_code[15].activity_code, "81", 2);
		strncpy(config_file.array_activity_code[16].activity_code, "89", 2);
		strncpy(config_file.array_activity_code[17].activity_code, "90", 2);
		strncpy(config_file.array_activity_code[18].activity_code, "92", 2);
		strncpy(config_file.array_activity_code[19].activity_code, "93", 2);
		strncpy(config_file.array_activity_code[20].activity_code, "94", 2);
		strncpy(config_file.array_activity_code[21].activity_code, "95", 2);
		strncpy(config_file.array_activity_code[22].activity_code, "99", 2);

		strncpy(config_file.array_driver_id[0].driver_id, "05158015775", 11);
		strncpy(config_file.array_driver_id[1].driver_id, "03855319669", 11);
		strncpy(config_file.array_driver_id[2].driver_id, "08347741603", 11);
		strncpy(config_file.array_driver_id[3].driver_id, "78081807500", 11);
		strncpy(config_file.array_driver_id[4].driver_id, "05451631611", 11);

		config_file.fw_version = 118;
		config_file.unidentified_driver_alarm_enabled = 1;
		config_file.idle_engine_cut_enabled = 0;
		config_file.idle_engine_cut_timer = 180;
		config_file.idle_engine_cut_period = 5;

		save_config();
	}

	return true;
}

void config_init(void) {
// create mutex
	osMutexDef(config_mutex);
	config_mutex_hanle = osMutexCreate(osMutex(config_mutex));
	if (config_mutex_hanle == NULL) {
		Error_Handler();
		return;
	}

// lock config by mutex
	config_mutex_take(osWaitForever);

	config_load_or_set_to_default();

// restore counters
	can_com_set_counters(config_file.rpm_green_count,
			config_file.rpm_yellow_count,
			config_file.rpm_red_count,
			config_file.downhill_count);

	can_com_set_turbo_counter(config_file.turbo_count);

	can_com_set_eng_counters(config_file.movement_timer,
			config_file.stopped_timer);
	rpm_set_odometer_ablosute_cnt(config_file.odometer_count);

// unlock config
	config_mutex_give();
}

config_file_t* config_file_get(void) {

	return &config_file;
}


bool config_mutex_take(uint32_t timeout_ms) {
	//if (osMutexWait(config_mutex_hanle, timeout_ms) == osOK) {
	return true;
	//}
	//return false;
}

bool config_mutex_give(void) {
	//if (osMutexRelease(config_mutex_hanle) == osOK) {
	return true;
	//}
	//return false;
}

