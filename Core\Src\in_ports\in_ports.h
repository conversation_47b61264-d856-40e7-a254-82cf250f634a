/****************************************************************************
 *   Copyright (C) 2020.01.22. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _IN_PORTS_H_
#define _IN_PORTS_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
void* in_ports_get_rx_message(void);
bool in_ports_init(void);
void time_in_ports_proc();

#endif // _DI_H_
