#MicroXplorer Configuration settings - do not modify
CAD.formats=[]
CAD.pinconfig=Dual
CAD.provider=
Dma.Request0=USART1_RX
Dma.Request1=USART1_TX
Dma.Request10=USART6_RX
Dma.Request11=USART6_TX
Dma.Request2=USART2_RX
Dma.Request3=USART2_TX
Dma.Request4=USART3_RX
Dma.Request5=USART3_TX
Dma.Request6=UART4_RX
Dma.Request7=UART4_TX
Dma.Request8=UART5_RX
Dma.Request9=UART5_TX
Dma.RequestsNb=12
Dma.UART4_RX.6.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART4_RX.6.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_RX.6.Instance=DMA1_Stream2
Dma.UART4_RX.6.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_RX.6.MemInc=DMA_MINC_ENABLE
Dma.UART4_RX.6.Mode=DMA_NORMAL
Dma.UART4_RX.6.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_RX.6.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_RX.6.Priority=DMA_PRIORITY_LOW
Dma.UART4_RX.6.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.UART4_TX.7.Direction=DMA_MEMORY_TO_PERIPH
Dma.UART4_TX.7.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_TX.7.Instance=DMA1_Stream4
Dma.UART4_TX.7.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_TX.7.MemInc=DMA_MINC_ENABLE
Dma.UART4_TX.7.Mode=DMA_NORMAL
Dma.UART4_TX.7.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_TX.7.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_TX.7.Priority=DMA_PRIORITY_LOW
Dma.UART4_TX.7.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.UART5_RX.8.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART5_RX.8.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_RX.8.Instance=DMA1_Stream0
Dma.UART5_RX.8.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_RX.8.MemInc=DMA_MINC_ENABLE
Dma.UART5_RX.8.Mode=DMA_NORMAL
Dma.UART5_RX.8.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_RX.8.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_RX.8.Priority=DMA_PRIORITY_LOW
Dma.UART5_RX.8.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.UART5_TX.9.Direction=DMA_MEMORY_TO_PERIPH
Dma.UART5_TX.9.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_TX.9.Instance=DMA1_Stream7
Dma.UART5_TX.9.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_TX.9.MemInc=DMA_MINC_ENABLE
Dma.UART5_TX.9.Mode=DMA_NORMAL
Dma.UART5_TX.9.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_TX.9.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_TX.9.Priority=DMA_PRIORITY_LOW
Dma.UART5_TX.9.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART1_TX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_TX.1.Instance=DMA2_Stream7
Dma.USART1_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_TX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_TX.1.Mode=DMA_NORMAL
Dma.USART1_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_TX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.2.Instance=DMA1_Stream5
Dma.USART2_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.2.Mode=DMA_NORMAL
Dma.USART2_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.2.Priority=DMA_PRIORITY_VERY_HIGH
Dma.USART2_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_TX.3.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART2_TX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_TX.3.Instance=DMA1_Stream6
Dma.USART2_TX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_TX.3.MemInc=DMA_MINC_ENABLE
Dma.USART2_TX.3.Mode=DMA_NORMAL
Dma.USART2_TX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_TX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_TX.3.Priority=DMA_PRIORITY_VERY_HIGH
Dma.USART2_TX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.4.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.4.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.4.Instance=DMA1_Stream1
Dma.USART3_RX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.4.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.4.Mode=DMA_NORMAL
Dma.USART3_RX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.4.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.4.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_TX.5.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART3_TX.5.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_TX.5.Instance=DMA1_Stream3
Dma.USART3_TX.5.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_TX.5.MemInc=DMA_MINC_ENABLE
Dma.USART3_TX.5.Mode=DMA_NORMAL
Dma.USART3_TX.5.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_TX.5.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_TX.5.Priority=DMA_PRIORITY_LOW
Dma.USART3_TX.5.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART6_RX.10.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART6_RX.10.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART6_RX.10.Instance=DMA2_Stream1
Dma.USART6_RX.10.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART6_RX.10.MemInc=DMA_MINC_ENABLE
Dma.USART6_RX.10.Mode=DMA_NORMAL
Dma.USART6_RX.10.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART6_RX.10.PeriphInc=DMA_PINC_DISABLE
Dma.USART6_RX.10.Priority=DMA_PRIORITY_LOW
Dma.USART6_RX.10.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART6_TX.11.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART6_TX.11.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART6_TX.11.Instance=DMA2_Stream6
Dma.USART6_TX.11.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART6_TX.11.MemInc=DMA_MINC_ENABLE
Dma.USART6_TX.11.Mode=DMA_NORMAL
Dma.USART6_TX.11.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART6_TX.11.PeriphInc=DMA_PINC_DISABLE
Dma.USART6_TX.11.Priority=DMA_PRIORITY_LOW
Dma.USART6_TX.11.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FREERTOS.IPParameters=Tasks01,configUSE_NEWLIB_REENTRANT
FREERTOS.Tasks01=defaultTask,0,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL
FREERTOS.configUSE_NEWLIB_REENTRANT=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F413RHT6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=FATFS
Mcu.IP10=TIM10
Mcu.IP11=UART4
Mcu.IP12=UART5
Mcu.IP13=USART1
Mcu.IP14=USART2
Mcu.IP15=USART3
Mcu.IP16=USART6
Mcu.IP17=USB_OTG_FS
Mcu.IP2=FREERTOS
Mcu.IP3=IWDG
Mcu.IP4=NVIC
Mcu.IP5=QUADSPI
Mcu.IP6=RCC
Mcu.IP7=SYS
Mcu.IP8=TIM6
Mcu.IP9=TIM7
Mcu.IPNb=18
Mcu.Name=STM32F413R(G-H)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC13
Mcu.Pin1=PH0 - OSC_IN
Mcu.Pin10=PC5
Mcu.Pin11=PB0
Mcu.Pin12=PB1
Mcu.Pin13=PB10
Mcu.Pin14=PB12
Mcu.Pin15=PB13
Mcu.Pin16=PB14
Mcu.Pin17=PC6
Mcu.Pin18=PC7
Mcu.Pin19=PC8
Mcu.Pin2=PH1 - OSC_OUT
Mcu.Pin20=PC9
Mcu.Pin21=PA8
Mcu.Pin22=PA9
Mcu.Pin23=PA10
Mcu.Pin24=PA11
Mcu.Pin25=PA12
Mcu.Pin26=PA13
Mcu.Pin27=PA14
Mcu.Pin28=PA15
Mcu.Pin29=PC10
Mcu.Pin3=PA0
Mcu.Pin30=PC11
Mcu.Pin31=PC12
Mcu.Pin32=PD2
Mcu.Pin33=PB3
Mcu.Pin34=PB4
Mcu.Pin35=PB5
Mcu.Pin36=PB6
Mcu.Pin37=PB8
Mcu.Pin38=PB9
Mcu.Pin39=VP_FATFS_VS_Generic
Mcu.Pin4=PA1
Mcu.Pin40=VP_FREERTOS_VS_CMSIS_V1
Mcu.Pin41=VP_IWDG_VS_IWDG
Mcu.Pin42=VP_SYS_VS_tim14
Mcu.Pin43=VP_TIM6_VS_ClockSourceINT
Mcu.Pin44=VP_TIM7_VS_ClockSourceINT
Mcu.Pin45=VP_TIM10_VS_ClockSourceINT
Mcu.Pin46=VP_TIM10_VS_OPM
Mcu.Pin5=PA2
Mcu.Pin6=PA3
Mcu.Pin7=PA6
Mcu.Pin8=PA7
Mcu.Pin9=PC4
Mcu.PinsNb=47
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F413RHTx
MxCube.Version=6.12.0
MxDb.Version=DB.6.0.120
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream1_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream2_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream3_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream4_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:5\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream6_IRQn=true\:5\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream7_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream6_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream7_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.OTG_FS_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.QUADSPI_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM6_DAC_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TIM7_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TIM8_TRG_COM_TIM14_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM8_TRG_COM_TIM14_IRQn
NVIC.TimeBaseIP=TIM14
NVIC.UART4_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UART5_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:8\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA0.Mode=Asynchronous
PA0.Signal=UART4_TX
PA1.Mode=Single Bank 1
PA1.Signal=QUADSPI_BK1_IO3
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.Mode=Device_Only
PA11.Signal=USB_OTG_FS_DM
PA12.Mode=Device_Only
PA12.Signal=USB_OTG_FS_DP
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_PuPd
PA15.GPIO_PuPd=GPIO_PULLDOWN
PA15.Locked=true
PA15.Signal=GPIO_Input
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA6.Locked=true
PA6.Signal=GPIO_Output
PA7.Locked=true
PA7.Signal=GPIO_Output
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.Locked=true
PB0.Signal=GPIO_Output
PB1.Mode=Single Bank 1
PB1.Signal=QUADSPI_CLK
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB12.Mode=Asynchronous
PB12.Signal=UART5_RX
PB13.Mode=Asynchronous
PB13.Signal=UART5_TX
PB14.Locked=true
PB14.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_PuPd
PB3.GPIO_PuPd=GPIO_PULLDOWN
PB3.Locked=true
PB3.Signal=GPIO_Input
PB4.GPIOParameters=GPIO_PuPd
PB4.GPIO_PuPd=GPIO_PULLDOWN
PB4.Locked=true
PB4.Signal=GPIO_Input
PB5.GPIOParameters=GPIO_PuPd
PB5.GPIO_PuPd=GPIO_PULLDOWN
PB5.Locked=true
PB5.Signal=GPIO_Input
PB6.Mode=Single Bank 1
PB6.Signal=QUADSPI_BK1_NCS
PB8.GPIOParameters=GPIO_PuPd
PB8.GPIO_PuPd=GPIO_PULLDOWN
PB8.Locked=true
PB8.Signal=GPIO_Input
PB9.GPIOParameters=GPIO_PuPd
PB9.GPIO_PuPd=GPIO_PULLDOWN
PB9.Locked=true
PB9.Signal=GPIO_Input
PC10.Mode=Single Bank 1
PC10.Signal=QUADSPI_BK1_IO1
PC11.Mode=Asynchronous
PC11.Signal=UART4_RX
PC12.GPIOParameters=GPIO_PuPd
PC12.GPIO_PuPd=GPIO_PULLDOWN
PC12.Locked=true
PC12.Signal=GPIO_Input
PC13.Locked=true
PC13.Signal=GPIO_Output
PC4.Locked=true
PC4.Signal=GPIO_Output
PC5.Mode=Asynchronous
PC5.Signal=USART3_RX
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PC8.Mode=Single Bank 1
PC8.Signal=QUADSPI_BK1_IO2
PC9.Mode=Single Bank 1
PC9.Signal=QUADSPI_BK1_IO0
PD2.GPIOParameters=GPIO_PuPd
PD2.GPIO_PuPd=GPIO_PULLDOWN
PD2.Locked=true
PD2.Signal=GPIO_Input
PH0\ -\ OSC_IN.Mode=HSE-External-Oscillator
PH0\ -\ OSC_IN.Signal=RCC_OSC_IN
PH1\ -\ OSC_OUT.Mode=HSE-External-Oscillator
PH1\ -\ OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F413RHTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=ProjetoMX.ioc
ProjectManager.ProjectName=ProjetoMX
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_QUADSPI_Init-QUADSPI-false-HAL-true,5-MX_UART4_Init-UART4-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true,7-MX_USART2_UART_Init-USART2-false-HAL-true,8-MX_USART3_UART_Init-USART3-false-HAL-true,9-MX_FATFS_Init-FATFS-false-HAL-false,10-MX_UART5_Init-UART5-false-HAL-true,11-MX_USART6_UART_Init-USART6-false-HAL-true,12-MX_USB_OTG_FS_PCD_Init-USB_OTG_FS-false-HAL-true,13-MX_TIM7_Init-TIM7-false-HAL-true,14-MX_TIM6_Init-TIM6-false-HAL-true,15-MX_IWDG_Init-IWDG-false-HAL-true
QUADSPI.ChipSelectHighTime=QSPI_CS_HIGH_TIME_2_CYCLE
QUADSPI.ClockPrescaler=1
QUADSPI.FlashSize=19
QUADSPI.IPParameters=ClockPrescaler,ChipSelectHighTime,FlashSize
RCC.AHBFreq_Value=96000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=48000000
RCC.APB1TimFreq_Value=96000000
RCC.APB2Freq_Value=96000000
RCC.APB2TimFreq_Value=96000000
RCC.CortexFreq_Value=96000000
RCC.DFSDM2AudioFreq_Value=48000000
RCC.DFSDM2Freq_Value=96000000
RCC.DFSDMAudioFreq_Value=48000000
RCC.DFSDMFreq_Value=96000000
RCC.FCLKCortexFreq_Value=96000000
RCC.FMPI2C1Freq_Value=48000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=96000000
RCC.HSE_VALUE=8000000
RCC.I2S1Freq_Value=48000000
RCC.I2S2Freq_Value=48000000
RCC.IPParameters=AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,DFSDM2AudioFreq_Value,DFSDM2Freq_Value,DFSDMAudioFreq_Value,DFSDMFreq_Value,FCLKCortexFreq_Value,FMPI2C1Freq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,I2S1Freq_Value,I2S2Freq_Value,LPTimerFreq_Value,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLI2SQCLKFreq_Value,PLLI2SRCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PLLRoutputFreq_Value,PLLSourceVirtual,PWRFreq_Value,RNGFreq_Value,SAI1AFreq_Value,SAI1BFreq_Value,SDIOFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,USBFreq_Value,VCOI2SInputFreq_Value,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTimerFreq_Value=48000000
RCC.MCO2PinFreq_Value=96000000
RCC.PLLCLKFreq_Value=96000000
RCC.PLLI2SQCLKFreq_Value=48000000
RCC.PLLI2SRCLKFreq_Value=48000000
RCC.PLLM=4
RCC.PLLN=96
RCC.PLLQ=4
RCC.PLLQCLKFreq_Value=48000000
RCC.PLLQoutputFreq_Value=48000000
RCC.PLLRCLKFreq_Value=96000000
RCC.PLLRoutputFreq_Value=96000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWRFreq_Value=96000000
RCC.RNGFreq_Value=48000000
RCC.SAI1AFreq_Value=8000000
RCC.SAI1BFreq_Value=8000000
RCC.SDIOFreq_Value=48000000
RCC.SYSCLKFreq_VALUE=96000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.USBFreq_Value=48000000
RCC.VCOI2SInputFreq_Value=500000
RCC.VCOI2SOutputFreq_Value=96000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=192000000
TIM7.IPParameters=Period
TIM7.Period=1000
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
USB_OTG_FS.IPParameters=VirtualMode
USB_OTG_FS.VirtualMode=Device_Only
VP_FATFS_VS_Generic.Mode=User_defined
VP_FATFS_VS_Generic.Signal=FATFS_VS_Generic
VP_FREERTOS_VS_CMSIS_V1.Mode=CMSIS_V1
VP_FREERTOS_VS_CMSIS_V1.Signal=FREERTOS_VS_CMSIS_V1
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_SYS_VS_tim14.Mode=TIM14
VP_SYS_VS_tim14.Signal=SYS_VS_tim14
VP_TIM10_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM10_VS_ClockSourceINT.Signal=TIM10_VS_ClockSourceINT
VP_TIM10_VS_OPM.Mode=OPM_bit
VP_TIM10_VS_OPM.Signal=TIM10_VS_OPM
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
VP_TIM7_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM7_VS_ClockSourceINT.Signal=TIM7_VS_ClockSourceINT
board=custom
rtos.0.ip=FREERTOS
isbadioc=false
