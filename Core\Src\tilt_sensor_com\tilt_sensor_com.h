/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _TILT_SENSOR_COMMUNICATION_H_
#define _TILT_SENSOR_COMMUNICATION_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
bool tilt_sensor_com_init(void);
void tilt_sensor_com_time_proc(void);
bool tilt_sensor_com_is_idle(void);

osMessageQId tilt_sensor_com_get_tx_messages_queue(void);

int tilt_sensor_get_roll(void);
int tilt_sensor_get_pitch(void);
int tilt_sensor_get_tpm(void);
bool tilt_sensor_get_rollover_alarm(void);

#endif // _TILT_SENSOR_COMMUNICATION_H_
