/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _TPMS_UART_H_
#define _TPMS_UART_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define TPMS_UART_RX_BUFF_SIZE       60

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
UART_HandleTypeDef* tpms_uart_get(void);

bool tpms_uart_init(void (*rx_cpl_cb)(uint8_t* data_p, size_t data_len));
bool tpms_uart_reinit();
void tpms_uart_msp_init(void);
void tpms_uart_msp_deinit(void);

bool tpms_uart_is_rx_started(void);
void tpms_uart_rx_cpl_cb(void);

bool tpms_uart_send(uint8_t* data, size_t data_len, void (*tx_cpl_cb)(void));
void tpms_uart_tx_cpl_cb(void);

void tpms_uart_err_cb(void);

void TPMS_UART_DMA_RX_IRQHandler(void);
void TPMS_UART_DMA_TX_IRQHandler(void);
void TPMS_UART_IRQHandler(void);

#endif // _TPMS_UART_H_
