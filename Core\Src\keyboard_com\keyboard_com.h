/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _KEYBOARD_COMMUNICATION_H_
#define _KEYBOARD_COMMUNICATION_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define KEYBOARD_UART_RX_BUFF_SIZE       256
/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/
extern bool driver_is_identified;

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
bool keyboard_com_init(void);
void keyboard_com_time_proc(void);
bool keyboard_com_is_idle(void);
void keyboard_start_process(void);
void update_driver_identification_status(void);
bool keyboard_com_get_ignition_state(void);

osMessageQId keyboard_com_get_tx_messages_queue(void);

void keyboard_start_process(void);

void keyboard_enter_sms_display_mode(void);
bool keyboard_is_ready_for_sms_display(void);

#endif // _KEYBOARD_COMMUNICATION_H_
