/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "tilt_sensor_com.h"
#include "../drivers/uart/tilt_sensor_uart.h"
#include "../audio_debug_com/audio_debug_com.h"
#include "../queue_mail/queue_mail.h"
#include "../queue_message/queue_message.h"
#include "../config/config.h"
#include "../watchdog/watchdog.h"
#include "../gps_com/gps_com.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define TILT_SENSOR_COM_RX_MAILS_QUEUE_SIZE         (10)  // Hold incoming mails from PERIPH5 till RX task processing it. So, should be not big(processing is fast)
#define TILT_SENSOR_COM_TX_MESSAGES_QUEUE_SIZE      (2)  // Hold incoming messages from GPS till uart is send data. So, should be not big(sending operation is fast)

#define TILT_SENSOR_COM_TX_END_SIGNAL               (1<<0)

#define TILT_SENSOR_IDLE_TIMEOUT_MS                 (15)

// related to Inclination Sensor WT61C Gyroscope
#define TILT_SENSOR_WT61C_HEADER  0x55
#define TILT_SENSOR_WT61C_ID      0x53

#define TILT_SENSOR_WT61C_HEADER_OFFS  0
#define TILT_SENSOR_WT61C_ID_OFFS      1
#define TILT_SENSOR_WT61C_ROLL_OFFS    2
#define TILT_SENSOR_WT61C_PITCH_OFFS   4
#define TILT_SENSOR_WT61C_YAW_OFFS     6
#define TILT_SENSOR_WT61C_TMP_OFFS     8
#define TILT_SENSOR_WT61C_SUM_OFFS     10
#define TILT_SENSOR_WT61C_MESSAGE_LEN       11
#define TILT_SENSOR_WT61C_ALIVE_TIMEOUT_MS  (3*1000)

#define TILT_COM_LOCK()                      osMutexWait(tilt_sensor_com.access_mux, osWaitForever)
#define TILT_COM_UNLOCK()                    osMutexRelease(tilt_sensor_com.access_mux)
/****************************************************************************
 * Private Types
 ****************************************************************************/
typedef struct {
	size_t size;
	uint8_t buff[TILT_SENSOR_UART_RX_BUFF_SIZE + 1];
} tilt_sensor_com_rx_mail_t; // Incoming mail from CAN

typedef struct {
	int roll;
	int pitch;
	int yaw;
	int tmp;
	bool rollover_alarm_f;
} WT61C_data_t;

typedef struct {
	osThreadId rx_task_handle;           // rx task handle(receive from periph5)
	osMailQId rx_queue_mail_handle;  // rx mails from irq(received from periph5)

	osThreadId tx_task_handle;             // tx task handle(send to periph5)
	osMessageQId tx_queue_message_handle;  // tx messages queue(send to periph5)

	uint8_t idle_timeout_ms;           // idle timeout for periph5 communication

	uint8_t sensor_message[TILT_SENSOR_WT61C_MESSAGE_LEN];  // sensor message
	uint8_t sensor_message_len;                         // message len
	WT61C_data_t sensor_data;                                // sensor data
	uint32_t sensor_alive_timeout_ms; // alive timeout for clr all sensor data if sensor is disconnected

	osMutexId access_mux;

} tilt_sensor_com_t;

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
// RX
static int tilt_sensor_sensor_data_to_angle(signed short data);
static int tilt_sensor_sensor_data_to_temperature(signed short data);
static uint8_t tilt_sensor_sensor_message_checksum_calc(uint8_t *message_p,
		uint8_t message_len);
static bool tilt_sensor_sensor_message_parse(uint8_t *message_p,
		uint8_t message_len);

void tilt_sensor_com_rx_cpl_cb(uint8_t *data_p, size_t data_len);
static void tilt_sensor_com_rx_thread(void const *argument);

// TX
static void tilt_sensor_com_tx_cpl_cb(void);
static void tilt_sensor_com_tx_thread(void const *argument);

/****************************************************************************
 * Private Data
 ****************************************************************************/
static tilt_sensor_com_t tilt_sensor_com;

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/
// RX
static int tilt_sensor_sensor_data_to_angle(signed short data) {
	float angle = data / 32768.0 * 180.0;
	return ((int) (angle));
}

static int tilt_sensor_sensor_data_to_temperature(signed short data) {
	float tmp = data / 340.0 + 36.53;
	return ((int) (tmp));
}

static uint8_t tilt_sensor_sensor_message_checksum_calc(uint8_t *message_p,
		uint8_t message_len) {
	if ((message_p == NULL) || (message_len == 0)) {
		// message is not correct
		return 0;
	}

	uint8_t checksum = 0;
	for (uint8_t i = 0; i < message_len; i++) {
		checksum += message_p[i];
	}

	return checksum;
}

static bool tilt_sensor_sensor_message_parse(uint8_t *message_p,
		uint8_t message_len) {
	// check in params
	// check message header
	if ((message_p[TILT_SENSOR_WT61C_HEADER_OFFS] != TILT_SENSOR_WT61C_HEADER)
			|| (message_p[TILT_SENSOR_WT61C_ID_OFFS] != TILT_SENSOR_WT61C_ID)) {
		// message header is not crrect
		return false;
	}

	// checksum check
	uint8_t checksum = tilt_sensor_sensor_message_checksum_calc(message_p,
			(message_len - 1));
	if (checksum != message_p[TILT_SENSOR_WT61C_SUM_OFFS]) {
		// wrong checksum
		return false;
	}

	TILT_COM_LOCK();

	// message is correct - get data
	tilt_sensor_com.sensor_data.roll = tilt_sensor_sensor_data_to_angle(
			(signed short) ((((uint16_t) message_p[TILT_SENSOR_WT61C_ROLL_OFFS
					+ 1]) << 8) | message_p[TILT_SENSOR_WT61C_ROLL_OFFS]));
	tilt_sensor_com.sensor_data.pitch = tilt_sensor_sensor_data_to_angle(
			(signed short) ((((uint16_t) message_p[TILT_SENSOR_WT61C_PITCH_OFFS
					+ 1]) << 8) | message_p[TILT_SENSOR_WT61C_PITCH_OFFS]));
	tilt_sensor_com.sensor_data.yaw = tilt_sensor_sensor_data_to_angle(
			(signed short) ((((uint16_t) message_p[TILT_SENSOR_WT61C_YAW_OFFS
					+ 1]) << 8) | message_p[TILT_SENSOR_WT61C_YAW_OFFS]));
	tilt_sensor_com.sensor_data.tmp = tilt_sensor_sensor_data_to_temperature(
			(signed short) ((((uint16_t) message_p[TILT_SENSOR_WT61C_TMP_OFFS
					+ 1]) << 8) | message_p[TILT_SENSOR_WT61C_TMP_OFFS]));

	TILT_COM_UNLOCK();

	config_file_t *config_file = config_file_get();
	config_mutex_take(osWaitForever);
	// get ROLL
	if ((tilt_sensor_com.sensor_data.roll > 0
	        && tilt_sensor_com.sensor_data.roll > config_file->roll_max
	    || tilt_sensor_com.sensor_data.roll < 0
	            && tilt_sensor_com.sensor_data.roll < (-1 * config_file->roll_max))) {
	    if (tilt_sensor_com.sensor_data.rollover_alarm_f == false) {
	        uint8_t alarme[16];  // Increased from 13 to 16

	        // Removed strroll buffer and directly format the complete string
	        snprintf((char *)alarme, sizeof(alarme), "ALM,110,%d*",
	                 tilt_sensor_com.sensor_data.roll);
			queue_message_put(gps_com_get_tx_messages_queue(),
										NULL, 0, alarme, strlen(alarme),
										NULL, 0, 0);

			tilt_sensor_com.sensor_data.rollover_alarm_f = true;
		}
	} else {
		tilt_sensor_com.sensor_data.rollover_alarm_f = false;
	}

	config_mutex_give();
	return true;
}

void tilt_sensor_com_rx_cpl_cb(uint8_t *data_p, size_t data_len) {

	if ((!data_p) || (data_len == 0))
		return;

// refresh idle timeout
	__disable_irq();
	tilt_sensor_com.idle_timeout_ms = TILT_SENSOR_IDLE_TIMEOUT_MS;
	__enable_irq();

// send mail
	queue_mail_put(tilt_sensor_com.rx_queue_mail_handle, data_p,
			TILT_SENSOR_WT61C_MESSAGE_LEN, 0);

}

static void tilt_sensor_com_rx_thread(void const *argument) {
	tilt_sensor_com_rx_mail_t *mail_p = NULL;

	while (1) {

		// get mail from can rx mail queue
		mail_p = (tilt_sensor_com_rx_mail_t*) queue_mail_get(
				tilt_sensor_com.rx_queue_mail_handle, osWaitForever);
		if (mail_p != NULL) {

			// log TILT sensor RX data
			audio_debug_com_send_log(mail_p->buff, mail_p->size,
			PERIPHERAL_UART_TILT_SENSOR_NUM, true);

			// processing of incomming data
			// collect message

			// message max len is come - parse message
			if (tilt_sensor_sensor_message_parse(mail_p->buff, mail_p->size)) {
				// message is correct
				// refresh alive timeout
				tilt_sensor_com.sensor_alive_timeout_ms =
				TILT_SENSOR_WT61C_ALIVE_TIMEOUT_MS;
			}

			// clr message len
			tilt_sensor_com.sensor_message_len = 0;
		}

		// free mail
		queue_mail_free(tilt_sensor_com.rx_queue_mail_handle, mail_p);
		mail_p = NULL;
	}

	watchdog_reset();
}

// TX
static void tilt_sensor_com_tx_cpl_cb(void) {
	osSignalSet(tilt_sensor_com.tx_task_handle, TILT_SENSOR_COM_TX_END_SIGNAL);
}

static void tilt_sensor_com_tx_thread(void const *argument) {
	bool tx_status = false;
	queue_message_t *message_p = NULL;

	while (1) {
		__disable_irq();
		// get message from tx message queue
		message_p = queue_message_get(tilt_sensor_com.tx_queue_message_handle,
		osWaitForever);

		__enable_irq();
		if (message_p != NULL) {

			// log TILT sensor TX data
			audio_debug_com_send_log(message_p->buff, message_p->size,
			PERIPHERAL_UART_TILT_SENSOR_NUM, true);

			// clr tx end signal
			osSignalWait(TILT_SENSOR_COM_TX_END_SIGNAL, 0);

			// send message
			tx_status = tilt_sensor_uart_send(message_p->buff, message_p->size,
					tilt_sensor_com_tx_cpl_cb);

			// wait end of tx
			if (tx_status) {
				osSignalWait(TILT_SENSOR_COM_TX_END_SIGNAL, osWaitForever);
			}

			// free message
			queue_message_free(message_p);
			message_p = NULL;
		}

		watchdog_reset();
	}
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
bool tilt_sensor_com_init(void) {

	// create mutex
	osMutexDef(tilt_mutex);
	tilt_sensor_com.access_mux = osMutexCreate(osMutex(tilt_mutex));
	if (tilt_sensor_com.access_mux == NULL) {
		Error_Handler();
		return false;
	}

	// RX
	// create rx mail queue
	osMailQDef(rx_mail_queue, TILT_SENSOR_COM_RX_MAILS_QUEUE_SIZE,
			tilt_sensor_com_rx_mail_t);
	tilt_sensor_com.rx_queue_mail_handle = osMailCreate(osMailQ(rx_mail_queue),
	NULL);
	if (tilt_sensor_com.rx_queue_mail_handle == NULL) {
		Error_Handler();
		return false;
	}

	// create rx task
	osThreadDef(tilt_sensor_rx_task, tilt_sensor_com_rx_thread,
			osPriorityNormal, 0, configMINIMAL_STACK_SIZE * 2);
	tilt_sensor_com.rx_task_handle = osThreadCreate(
			osThread(tilt_sensor_rx_task), NULL);
	if (tilt_sensor_com.rx_task_handle == NULL) {
		Error_Handler();
		return false;
	}

	if (!tilt_sensor_uart_init(tilt_sensor_com_rx_cpl_cb)) {
		Error_Handler();
		return false;
	}

	return true;
}

void tilt_sensor_com_time_proc(void) {
	if (tilt_sensor_com.idle_timeout_ms > 0) {
		tilt_sensor_com.idle_timeout_ms--;
	}

	if (tilt_sensor_com.sensor_alive_timeout_ms > 0) {
		tilt_sensor_com.sensor_alive_timeout_ms--;
		if (tilt_sensor_com.sensor_alive_timeout_ms == 0) {
			tilt_sensor_com.sensor_data.roll = 0;
			tilt_sensor_com.sensor_data.pitch = 0;
			tilt_sensor_com.sensor_data.yaw = 0;
			tilt_sensor_com.sensor_data.tmp = 0;
			tilt_sensor_com.sensor_data.rollover_alarm_f = false;
		}
	}
}

bool tilt_sensor_com_is_idle(void) {
	if ((tilt_sensor_com.idle_timeout_ms == 0)) {
		return true;
	}
	return false;
}

osMessageQId tilt_sensor_com_get_tx_messages_queue(void) {
	return tilt_sensor_com.tx_queue_message_handle;
}

int tilt_sensor_get_roll(void) {
	return tilt_sensor_com.sensor_data.roll;
}

int tilt_sensor_get_pitch(void) {
	return tilt_sensor_com.sensor_data.pitch;
}

int tilt_sensor_get_tpm(void) {
	return tilt_sensor_com.sensor_data.tmp;
}

bool tilt_sensor_get_rollover_alarm(void) {
	return tilt_sensor_com.sensor_data.rollover_alarm_f;
}
