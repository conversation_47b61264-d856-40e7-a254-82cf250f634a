/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "tilt_sensor_uart.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define UART_NUMBER                 USART6
#define UART_BAUDRATE               115200

#define UART_CLK_ENABLE()           __HAL_RCC_USART6_CLK_ENABLE()
#define UART_CLK_DISABLE()          __HAL_RCC_USART6_CLK_DISABLE()

#define UART_RX_GPIO_CLK_ENABLE()   __HAL_RCC_GPIOD_CLK_ENABLE()
#define UART_TX_GPIO_CLK_ENABLE()   __HAL_RCC_GPIOC_CLK_ENABLE()

#define tilt_sensor_uart_IRQHandler         USART6_IRQHandler
/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void);

/****************************************************************************
 * Private Data
 ****************************************************************************/
UART_HandleTypeDef huart6;
static void (*tx_cpl_cb_p)(void) = NULL;
static void (*rx_cpl_cb_p)(uint8_t *data_p, size_t data_len) = NULL;
static uint8_t rx_data[TILT_SENSOR_UART_RX_BUFF_SIZE];

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void) {
	__disable_irq();

	HAL_StatusTypeDef status = HAL_UARTEx_ReceiveToIdle_DMA(&huart6, rx_data,
			TILT_SENSOR_UART_RX_BUFF_SIZE);

	__enable_irq();

	return status;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
UART_HandleTypeDef* tilt_sensor_uart_get(void) {
	return &huart6;
}

bool tilt_sensor_uart_init(void (*rx_cpl_cb)(uint8_t *data_p, size_t data_len)) {
	huart6.Instance = USART6;
	huart6.Init.BaudRate = 115200;
	huart6.Init.WordLength = UART_WORDLENGTH_8B;
	huart6.Init.StopBits = UART_STOPBITS_1;
	huart6.Init.Parity = UART_PARITY_NONE;
	huart6.Init.Mode = UART_MODE_TX_RX;
	huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	huart6.Init.OverSampling = UART_OVERSAMPLING_16;
	if (HAL_UART_Init(&huart6) != HAL_OK) {
		Error_Handler();
	}

	// start rx
	rx_cpl_cb_p = rx_cpl_cb;
	if (uart_start_rx() != HAL_OK) {
		return false;
	}

	return true;
}

bool tilt_sensor_uart_send(uint8_t *data_p, size_t data_len,
		void (*tx_cpl_cb)(void)) {
	if ((data_p == NULL) || (data_len == 0)) {
		return false;
	}
	tx_cpl_cb_p = tx_cpl_cb;
	if (HAL_UART_Transmit_IT(&huart6, data_p, data_len) != HAL_OK) {
		tx_cpl_cb_p = NULL;
		return false;
	}
	return true;
}

bool tilt_sensor_uart_is_rx_started(void) {
	if ((huart6.RxState == HAL_UART_STATE_BUSY_RX)
			&& (huart6.RxXferCount < TILT_SENSOR_UART_RX_BUFF_SIZE)) {
		return true;
	}
	return false;
}

void tilt_sensor_uart_rx_cpl_cb(void) {
	if (rx_cpl_cb_p != NULL) {
		// calculate rx len
		size_t rx_len = huart6.hdmarx->Instance->NDTR;
		if (rx_len > TILT_SENSOR_UART_RX_BUFF_SIZE) {
			rx_len = TILT_SENSOR_UART_RX_BUFF_SIZE;
		}
		rx_len = TILT_SENSOR_UART_RX_BUFF_SIZE - rx_len;

		// notify about new data
		if (rx_len > 0) { // check to prevent execute cp second time
			rx_cpl_cb_p(rx_data, rx_len);
		}
	}
	// start rx again
	uart_start_rx();
}

void tilt_sensor_uart_tx_cpl_cb(void) {
	__disable_irq();

	if (tx_cpl_cb_p != NULL) {
		tx_cpl_cb_p();
		tx_cpl_cb_p = NULL;
	}

	__enable_irq();
}

void tilt_sensor_uart_err_cb(void) {
	// abort
	__HAL_UART_FLUSH_DRREGISTER(&huart6);
	HAL_UART_Abort(&huart6);

	// do tx_cpl_cb to unlock any waiting tasks
	tilt_sensor_uart_tx_cpl_cb();

	// start rx again
	uart_start_rx();
}

void tilt_sensor_uart_IRQHandler(void) {
	// check for idle irq
	bool is_idle = false;
	if (__HAL_UART_GET_FLAG(&huart6, UART_FLAG_IDLE)) {
		is_idle = true;
	}

	// irq processing
	HAL_UART_IRQHandler(&huart6);

	// process idle irq
	if (is_idle) {
		__HAL_UART_CLEAR_IDLEFLAG(&huart6);
		HAL_UART_AbortReceive(&huart6);
		HAL_UART_RxCpltCallback(&huart6);
	}
}
