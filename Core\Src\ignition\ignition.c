/****************************************************************************
 *   Copyright (C) 2020.01.22. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "ignition.h"
#include "../can_com/can_com.h"
#include "../keyboard_com/keyboard_com.h"
#include "../audio_debug_com/audio_debug_com.h"
#include "../gps_com/gps_com.h"
/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Private Data
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

bool ignition = false;

/****************************************************************************
 * Private Functions
 ****************************************************************************/
void ignition_turnon() {
	keyboard_start_process();
}

void ignition_turnoff() {
    keyboard_off_ignition();
    gps_com_reset_notification_state();  // Add this line
}
/****************************************************************************
 * Public Functions
 ****************************************************************************/

void ignition_time_proc(void) {
	if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_15) == GPIO_PIN_SET && ignition == true) {
		can_com_set_in_stat(3, 0);
		ignition = false;
		ignition_turnoff();
	} else if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_15)
			== GPIO_PIN_RESET&& ignition == false) {
		ignition = true;
		ignition_turnon();
		can_com_set_in_stat(3, 1);
	}
}

