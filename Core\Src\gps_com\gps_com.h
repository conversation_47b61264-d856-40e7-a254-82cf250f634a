/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _GPS_COMMUNICATION_H_
#define _GPS_COMMUNICATION_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"
#include "../speed_control/speed_control.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
bool gps_com_init(void);
void gps_com_time_proc(void);
bool gps_com_is_idle(void);

void gps_com_cmd_processing(uint8_t* data_p,
                            size_t data_len,
                            uint8_t uart_num,
                            void (*ack_send_func_p)(const char* ack_p));

void gps_com_reset_notification_state(void);

osMessageQId gps_com_get_tx_messages_queue(void);
int gps_com_get_speed_gps();
int calc_NMEA_Checksum( char *buf, int cnt );

#endif // _GPS_COMMUNICATION_H_
