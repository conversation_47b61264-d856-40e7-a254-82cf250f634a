#ifndef _KEYBOARD_UART_H_
#define _KEYBOARD_UART_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define KEYBOARD_UART_RX_BUFF_SIZE      50

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
UART_HandleTypeDef* keyboard_uart_get(void);

bool keyboard_uart_init(void (*rx_cpl_cb)(uint8_t* data_p, size_t data_len));
bool keyboard_uart_reinit();
void keyboard_uart_msp_init(void);
void keyboard_uart_msp_deinit(void);

bool keyboard_uart_is_rx_started(void);
void keyboard_uart_rx_cpl_cb(void);

bool keyboard_uart_send(uint8_t* data, size_t data_len);
void keyboard_uart_tx_cpl_cb(void);

void keyboard_uart_err_cb(void);

void KEYBOARD_UART_DMA_RX_IRQHandler(void);
void KEYBOARD_UART_DMA_TX_IRQHandler(void);
void KEYBOARD_UART_IRQHandler(void);

void keyboard_off_ignition(void);

#endif // _KEYBOARD_UART_H_
