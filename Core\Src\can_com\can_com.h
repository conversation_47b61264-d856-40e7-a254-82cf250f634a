/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _CAN_COMMUNICATION_H_
#define _CAN_COMMUNICATION_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
bool can_com_init(void);
void can_com_time_proc(void);
bool can_com_is_idle(void);
void* can_com_get_rx_message(bool* is_idle_p);
osMessageQId can_com_get_rx_messages_queue(void);
osMessageQId can_com_get_tx_messages_queue(void);

void can_com_set_counters(uint32_t green_cntr,
                          uint32_t yellow_cntr,
                          uint32_t red_cntr,
                          uint32_t neutral_downhill_cntr
);

void can_com_set_turbo_counter(uint32_t pressureOverLimitDuration);

void can_com_set_eng_counters(uint64_t movement_timer,
                              uint64_t stopped_timer);

uint8_t can_com_get_speed(void);

void can_com_emulation_proc();

void can_com_save_counters(void);

void can_com_set_in_stat(int port, int value);

void can_com_set_out_stat(int port, int value);

void check_engine_idle_state(uint8_t speed, uint16_t rpm);

#endif // _CAN_COMMUNICATION_H_
