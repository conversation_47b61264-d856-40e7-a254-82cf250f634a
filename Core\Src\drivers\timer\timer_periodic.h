/****************************************************************************
 *   Copyright (C) 2020.01.03 All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _TIMER_PERIODIC_H_
#define _TIMER_PERIODIC_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define PERIODIC_TIMER_IRQHandler   TIM3_IRQHandler

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
TIM_HandleTypeDef* timer_periodic_get(void);

bool timer_periodic_init(void);
void timer_periodic_msp_init(void);
void timer_periodic_msp_deinit(void);

void timer_periodic_cb(void);

void PERIODIC_TIMER_IRQHandler(void);

#endif //_TIMER_PERIODIC_H_