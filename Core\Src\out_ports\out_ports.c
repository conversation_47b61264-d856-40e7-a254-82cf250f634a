/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "out_ports.h"
#include "../config/config.h"
#include "../watchdog/watchdog.h"
#include "../can_com/can_com.h"
#include "../queue_message/queue_message.h"
#include "../gps_com/gps_com.h"
/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Private Types
 ****************************************************************************/

typedef struct {
	osThreadId out_port_handle;
	osThreadId buzzer_handle;

	int out2_turnedon;
	int out3_turnedon;
	int out4_turnedon;
	int out5_turnedon;

	int out2_turnoff;
	int out3_turnoff;
	int out4_turnoff;
	int out5_turnoff;

	int out2_time;
	int out3_time;
	int out4_time;
	int out5_time;
} out_ports_t;

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Private Data
 ****************************************************************************/
static out_ports_t out_ports;
static int buzzer_on;
/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Public Functions
 ****************************************************************************/

static void out_port_thread(const void *arg) {
	for (;;) {

		if (out_ports.out2_turnoff == 1 || out_ports.out3_turnoff == 1
				|| out_ports.out4_turnoff == 1 || out_ports.out5_turnoff == 1) {
			config_file_t *config_file = config_file_get();

			config_mutex_take(osWaitForever);
			{
				if (out_ports.out2_turnoff == 1) {
					out_ports.out2_turnoff = 0;
					config_file->out2_enabled = 0;

					uint8_t alarme[10];
					memset(alarme, 0, sizeof alarme);
					strncpy(alarme, "ALM,142,", 8);
					strncat(alarme, "0", 1);
					strncat(alarme, "*", 1);
					queue_message_put(gps_com_get_tx_messages_queue(), NULL, 0,
							alarme, strlen(alarme), NULL, 0, 0);
				}
				if (out_ports.out3_turnoff == 1) {
					out_ports.out3_turnoff = 0;
					config_file->out3_enabled = 0;

					uint8_t alarme[10];
					memset(alarme, 0, sizeof alarme);
					strncpy(alarme, "ALM,143,", 8);
					strncat(alarme, "0", 1);
					strncat(alarme, "*", 1);
					queue_message_put(gps_com_get_tx_messages_queue(), NULL, 0,
							alarme, strlen(alarme), NULL, 0, 0);
				}
				if (out_ports.out4_turnoff == 1) {
					out_ports.out4_turnoff = 0;
					config_file->out4_enabled = 0;

					uint8_t alarme[10];
					memset(alarme, 0, sizeof alarme);
					strncpy(alarme, "ALM,144,", 8);
					strncat(alarme, "0", 1);
					strncat(alarme, "*", 1);
					queue_message_put(gps_com_get_tx_messages_queue(), NULL, 0,
							alarme, strlen(alarme), NULL, 0, 0);
				}
				if (out_ports.out5_turnoff == 1) {
					out_ports.out5_turnoff = 0;
					config_file->out5_enabled = 0;

					uint8_t alarme[10];
					memset(alarme, 0, sizeof alarme);
					strncpy(alarme, "ALM,145,", 8);
					strncat(alarme, "0", 1);
					strncat(alarme, "*", 1);
					queue_message_put(gps_com_get_tx_messages_queue(), NULL, 0,
							alarme, strlen(alarme), NULL, 0, 0);
				}

				save_config();
			}
			config_mutex_give();
		}
		watchdog_reset();

	} // thread loop
}

static void buzzer_thread(const void *arg) {
	for (;;) {

		if (buzzer_on == 1) {
			HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_SET);
			osDelay(60);
			HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_RESET);
			osDelay(60);
			HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_SET);
			osDelay(60);
			HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_RESET);
			osDelay(60);
			HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_SET);
			osDelay(60);
			HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_RESET);
			osDelay(700);
		}

		watchdog_reset();

	} // thread loop
}

void set_buzzer(int buzzer_on_stat) {
	buzzer_on = buzzer_on_stat;
}

bool out_ports_init(void) {

	buzzer_on = 0;

	out_ports.out2_turnoff = 0;
	out_ports.out3_turnoff = 0;
	out_ports.out4_turnoff = 0;
	out_ports.out5_turnoff = 0;

	out_ports.out2_time = 0;
	out_ports.out3_time = 0;
	out_ports.out4_time = 0;
	out_ports.out5_time = 0;

	osThreadDef(out_port_task, out_port_thread, osPriorityNormal, 0,
			configMINIMAL_STACK_SIZE * 2);
	out_ports.out_port_handle = osThreadCreate(osThread(out_port_task), NULL);
	if (out_ports.out_port_handle == NULL) {
		Error_Handler();
		return false;
	}

	osThreadDef(buzzer_task, buzzer_thread, osPriorityNormal, 0,
			configMINIMAL_STACK_SIZE * 2);
	out_ports.buzzer_handle = osThreadCreate(osThread(buzzer_task), NULL);
	if (out_ports.buzzer_handle == NULL) {
		Error_Handler();
		return false;
	}
}
void time_out_ports_proc() {
	config_file_t *config_file = config_file_get();
	config_mutex_take(osWaitForever);
	//out2
	if (config_file->out2_enabled == 1) {
		if (config_file->out2_time > 0
				&& out_ports.out2_time > config_file->out2_time) {
			out_ports.out2_turnoff = 1;
		}

		out_ports.out2_time++;
		HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_SET);

		can_com_set_out_stat(2, 1);
		out_ports.out2_turnedon = true;

	} else {
		if (out_ports.out2_turnedon == true) {
			out_ports.out2_time = 0;
			HAL_GPIO_WritePin(GPIOA, GPIO_PIN_6, GPIO_PIN_RESET);
			out_ports.out2_turnedon = false;
			can_com_set_out_stat(2, 0);
		}
	}

	//out3
	if (config_file->out3_enabled == 1) {
		if (config_file->out3_time > 0
				&& out_ports.out3_time > config_file->out3_time) {
			out_ports.out3_turnoff = 1;
		}

		out_ports.out3_time++;
		HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_SET);
		can_com_set_out_stat(3, 1);
		out_ports.out3_turnedon = true;
	} else {
		if (out_ports.out3_turnedon == true) {
			out_ports.out3_time = 0;
			HAL_GPIO_WritePin(GPIOA, GPIO_PIN_7, GPIO_PIN_RESET);
			can_com_set_out_stat(3, 0);
		}
	}

	//out4
	if (config_file->out4_enabled == 1) {
		if (config_file->out4_time > 0
				&& out_ports.out4_time > config_file->out4_time) {
			out_ports.out4_turnoff = 1;
		}

		out_ports.out4_time++;
		HAL_GPIO_WritePin(GPIOC, GPIO_PIN_4, GPIO_PIN_SET);
		can_com_set_out_stat(4, 1);
		out_ports.out4_turnedon = true;
	} else {
		if (out_ports.out4_turnedon == true) {
			out_ports.out4_time = 0;
			HAL_GPIO_WritePin(GPIOC, GPIO_PIN_4, GPIO_PIN_RESET);
			can_com_set_out_stat(4, 0);
		}
	}

	//out5
	if (config_file->out5_enabled == 1) {
		if (config_file->out5_time > 0
				&& out_ports.out5_time > config_file->out5_time) {
			out_ports.out5_turnoff = 1;
		}

		out_ports.out5_time++;
		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_SET);
		can_com_set_out_stat(5, 1);
		out_ports.out5_turnedon = true;
	} else {
		if (out_ports.out5_turnedon == true) {
			out_ports.out5_time = 0;
			HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_RESET);
			can_com_set_out_stat(5, 0);
		}
	}
	config_mutex_give();
}
