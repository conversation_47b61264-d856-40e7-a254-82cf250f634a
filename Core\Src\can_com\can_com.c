/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "can_com.h"
#include "../drivers/uart/can_uart.h"
#include "../queue_mail/queue_mail.h"
#include "../queue_message/queue_message.h"
#include "../config/config.h"
#include "../out_ports/out_ports.h"
#include "../rpm/rpm.h"
#include "../audio_debug_com/audio_debug_com.h"
#include "../tilt_sensor_com/tilt_sensor_com.h"
#include "../watchdog/watchdog.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define CAN_COM_RX_MAILS_QUEUE_SIZE         (15)  // Hold incoming mails from CAN till RX task processing it. So, should be not big(processing is fast)
#define CAN_COM_RX_MESSAGES_QUEUE_SIZE      (15) // Hold incoming messages(wrapped) from CAN till GPS task processing data from other peripherals. So, should be big enough.
#define CAN_COM_TX_MESSAGES_QUEUE_SIZE      (15)  // Hold incoming messages from GPS till uart is send data. So, should be not big(sending operation is fast)

#define CAN_COM_TX_END_SIGNAL               (1<<0)

#define CAN_IDLE_TIMEOUT_MS                 (15)

// can message definitions
#define CAN_MESSAGE_BEGIN                   "FR1"

// engine on/off commands
#define CAN_ENGINE_OFF_CMD                  (0)
#define CAN_ENGINE_ON_CMD                   (1)
#define CAN_ENGINE_ON1_CMD                  (2)

#define CAN_ENGINE_COMMON_UNDEFINED_STATE   (0)
#define CAN_ENGINE_COMMON_OFF_STATE         (1)
#define CAN_ENGINE_COMMON_ON_STATE          (2)

#define CAN_SPEED_CONVERSION_FACTOR         (1.61)

#define CAN_IS_DI_INVERTED                  true

#define CAN_COM_LOCK()                      osMutexWait(can_com.access_mux, osWaitForever)
#define CAN_COM_UNLOCK()                    osMutexRelease(can_com.access_mux)
/****************************************************************************
 * Private Types
 ****************************************************************************/
typedef struct {
	size_t size;
	uint8_t buff[CAN_UART_RX_BUFF_SIZE + 1];
} can_com_rx_mail_t; // Incoming mail from CAN

typedef struct {
	osThreadId rx_task_handle;             // rx task handle(receive from can)
	osMailQId rx_queue_mail_handle;      // rx mails from irq(received from can)
	osMessageQId rx_queue_message_handle; // rx messages(received from can and processed)

	osThreadId tx_task_handle;             // tx task handle(send to can)
	osMessageQId tx_queue_message_handle;    // tx messages queue(send to can)

	uint16_t idle_timeout_ms;            // idle timeout for can communication
	uint16_t idle_timeout_prev_ms;       // prev value of idle timeout

	uint32_t rpm_green_zone_cntr;          // rpm green zone counter
	uint32_t rpm_yellow_zone_cntr;         // rpm yellow zone counter
	uint32_t rpm_red_zone_cntr;            // rpm red zone counter
	uint32_t neutral_downhill_cntr;        // neutral downhill counter
	uint32_t neutral_downhill_cntr_alarm;      // neutral downhill counter alarm
	uint16_t neutral_downhill_start_cntr;  // neutral start downhill counter
	uint8_t engine_curr_state;            // current engine state

	uint32_t turboPressureOverLimitDuration;
	bool turboPressureOverLimitWarn;
	int turbo_Time;

	bool gasKickdownFirst;
	uint32_t gasKickdownCntr;

	uint64_t movement_timer;               // engine movement timer
	uint64_t stopped_timer;                // engine stopped timer

	uint32_t odometer;

	uint8_t fuel_tank;
	uint8_t speed;

	bool in3;
	bool in4;
	bool in5;
	bool in6;
	bool in7;
	bool in8;
	bool in9;
	bool in10;
	bool out2;
	bool out3;
	bool out4;
	bool out5;

	int count_vel_1;
	bool alarm_vel_1;

	int count_vel_2;
	bool alarm_vel_2;

	bool beep_on_vel_1;
	bool beep_on_vel_2;

	bool engine_alarme_on;

	bool rpm_limit_alarm;

	osMutexId access_mux;                   // access mutex for some CAN context
} can_com_t;

// Forward declarations

static bool can_com_speed_value_replace(uint8_t *original_p,
		size_t original_size, uint8_t *buff_p, size_t buff_max_size,
		size_t *size_new_p);

static int can_com_parse_message(uint8_t *payload_start_p,
		uint32_t payload_size);

// RX
void can_com_rx_cpl_cb(uint8_t *data_p, size_t data_len);
static void can_com_rx_thread(void const *argument);

// TX
static void can_com_tx_cpl_cb(void);
static void can_com_tx_thread(void const *argument);

/****************************************************************************
 * Private Data
 ****************************************************************************/
static can_com_t can_com = { .speed = 0xff,  // set speed value to default state
		.turboPressureOverLimitWarn = false, .gasKickdownFirst = true };

/****************************************************************************
 * Public Data
 ****************************************************************************/
static uint32_t engine_idle_counter = 0;
static uint32_t engine_shutdown_counter = 0;
static bool engine_shutdown_active = false;
/****************************************************************************
 * Private Functions
 ****************************************************************************/

// This function replaces speed value with speed*factor
// Note: this function may affect can_com.speed
static bool can_com_speed_value_replace(uint8_t *original_p,
		size_t original_size, uint8_t *buff_p, size_t buff_max_size,
		size_t *size_new_p) {

	// check IN params
	if ((!original_p) || (original_size == 0) || (!buff_p)
			|| (buff_max_size == 0) || (!size_new_p)
			|| (buff_max_size < original_size))
		return false; // invalid params

	uint8_t buff[20] = { 0 };
	memset(buff_p, 0, buff_max_size);

	config_file_t *config_file = config_file_get();
	config_mutex_take(osWaitForever);

	// search for CAN message start

	char *start_p = strstr((char*) original_p, CAN_MESSAGE_BEGIN);

	if (!start_p)		// if no message start
		return false;

	char *end_p = start_p;

	// get speed
	for (uint8_t i = 0; i < 7; i++) { // 7, becouse speed parameter after 4 other params
		start_p = end_p + 1;
		end_p = strstr(start_p, ",");
		if (end_p == NULL) {		// if error
			config_mutex_give();
			return false;
		}
	}

	{
		uint16_t speed = 0;
		int len = end_p - start_p;
		if ((len > 0) && (len < sizeof(buff))) {
			memcpy(buff, start_p, len);
			int num = atoi((char const*) buff);
			speed = (uint16_t) num;
		} else {
			config_mutex_give();
			// error
			return false;
		}

		// convert speed
		speed = (uint16_t) (((float) speed) / CAN_SPEED_CONVERSION_FACTOR);
		memset(buff, 0, sizeof(buff));
		int ret = snprintf((char*) buff, (sizeof(buff) - 1), "%u", speed);
		if ((ret <= 0) || (ret >= sizeof(buff))) { // error while print new speed value
			config_mutex_give();
			return false;
		}
		if (config_file->pulse_enabled == 0) // save speed after conversion (only if pulse_conversion is not activated)
			can_com.speed = (uint8_t) speed;
	}

	// compose new message ---
	int remain = buff_max_size;

	// part before speed
	int pointer = (uint32_t) start_p - (uint32_t) original_p;

	if ((pointer <= 0) || (pointer > remain)) {
		config_mutex_give();
		return false; // error
	}

	memcpy(buff_p, original_p, pointer);
	remain -= pointer;

	// speed part
	if (strlen((char const*) buff) > remain) {
		config_mutex_give();
		return false; // error
	}
	memcpy(&buff_p[pointer], buff, strlen((char const*) buff));
	pointer += strlen((char const*) buff);
	remain -= strlen((char const*) buff);

	// the rest part
	int origin_rest = (uint32_t) end_p - (uint32_t) original_p;

	if (origin_rest <= 0) {
		config_mutex_give();
		return false; // error
	}

	origin_rest = original_size - origin_rest;

	if ((origin_rest <= 0) || (origin_rest > remain)) {
		config_mutex_give();
		return false; // error
	}
	memcpy(&buff_p[pointer], end_p, origin_rest);

	*size_new_p = pointer + origin_rest;

	config_mutex_give();
	return true;

}

static int can_com_parse_message(uint8_t *payload_start_p,
		uint32_t payload_size) {

	if ((!payload_start_p) || (payload_size == 0))
		return 0;

	int len = 0;
	int num = 0;
	uint8_t buff[20];

	// search for can message start
	char *start_p = strstr((char*) payload_start_p, CAN_MESSAGE_BEGIN);
	char *end_p = 0;

	if (!start_p)
		return 0; // no message start

	// check engine state
	start_p += strlen(CAN_MESSAGE_BEGIN) + 1; // +1 - len of values separator ','
	end_p = strstr(start_p, ",");
	if (!end_p)
		return 0; // error

	// Engine State

	uint8_t engine_state = 0;
	len = end_p - start_p;
	if (len > 0) {
		memset(buff, 0, sizeof(buff));
		memcpy(buff, start_p, len);
		num = atoi((char const*) buff);
		engine_state = (uint8_t) num;
	}

	config_file_t *config_file = config_file_get();

	if (engine_state == CAN_ENGINE_OFF_CMD) {

		// engine is OFF
		can_com.neutral_downhill_start_cntr = 0;
		can_com.turboPressureOverLimitWarn = false;
		if (can_com.engine_curr_state != CAN_ENGINE_COMMON_OFF_STATE) {
			// engine just OFF
			can_com.engine_curr_state = CAN_ENGINE_COMMON_OFF_STATE;
			// store current counters at FLASH
			//RETIRADO PARA TESTES
			/*config_mutex_take(osWaitForever);
			 {
			 config_file->rpm_green_count = can_com.rpm_green_zone_cntr;
			 config_file->rpm_yellow_count = can_com.rpm_yellow_zone_cntr;
			 config_file->rpm_red_count = can_com.rpm_red_zone_cntr;
			 config_file->downhill_count = can_com.neutral_downhill_cntr;

			 config_file->odometer_count = rpm_get_odometer_ablosute_cnt();
			 config_file->stopped_timer = can_com.stopped_timer;
			 config_file->movement_timer = can_com.movement_timer;

			 config_file->turbo_count =
			 can_com.turboPressureOverLimitDuration;

			 save_config();
			 }
			 */
			config_mutex_give();

			return 0;
		}
		// skip others values because engine is OFF

	} else {

		// engine ON
		if (can_com.engine_curr_state != CAN_ENGINE_COMMON_ON_STATE) {

			// engine just ON
			can_com.engine_curr_state = CAN_ENGINE_COMMON_ON_STATE;

			// restore counters from flash(config contain latest values)
			can_com.rpm_green_zone_cntr = config_file->rpm_green_count;
			can_com.rpm_yellow_zone_cntr = config_file->rpm_yellow_count;
			can_com.rpm_red_zone_cntr = config_file->rpm_red_count;
			can_com.neutral_downhill_cntr = config_file->downhill_count;
			can_com.turboPressureOverLimitDuration = config_file->turbo_count;

			rpm_set_odometer_ablosute_cnt(config_file->odometer_count);
			can_com.stopped_timer = config_file->stopped_timer;
			can_com.movement_timer = config_file->movement_timer;

		}
	}

	config_mutex_take(osWaitForever);

	if (config_file->pulse_enabled > 0) {
		// do not processing incomming messages any more
		config_mutex_give();
		return 0;
	}

	//Get ODOMETER
	start_p = end_p + 1;
	end_p = strstr(start_p, ",");
	if (!end_p) {
		config_mutex_give();
		return 0; // error
	}
	uint32_t odometer = 0;
	len = end_p - start_p;
	if (len > 0) {
		memset(buff, 0, sizeof(buff));
		memcpy(buff, start_p, len);
		num = atoi((char const*) buff);
		odometer = (uint32_t) num;
	}

	// get fuel tank
	for (uint8_t i = 0; i < 3; i++) { // 3, because speed parameter after 2 other params
		start_p = end_p + 1;
		end_p = strstr(start_p, ",");
		if (end_p == NULL) {
			config_mutex_give();
			return 0; // error
		}
	}

	uint8_t fuel_tank = 0;
	len = end_p - start_p;
	if (len > 0) {
		memset(buff, 0, sizeof(buff));
		memcpy(buff, start_p, len);
		num = atoi((char const*) buff);
		fuel_tank = (uint8_t) num;

		if (fuel_tank > 100 || fuel_tank < 0) {

			return 0;
		}
	}

	start_p = end_p + 1;
	end_p = strstr(start_p, ",");
	if (!end_p) {
		config_mutex_give();
		return 0; // error
	}

	// get speed
	uint8_t speed = 0;
	len = end_p - start_p;
	if (len > 0) {
		memset(buff, 0, sizeof(buff));
		memcpy(buff, start_p, len);
		num = atoi((char const*) buff);
		speed = (uint8_t) num;

		if (speed < 0 || speed > 200) {

			config_mutex_give();
			return 0;
		}
	}
	if (config_file->speed_type == 1) {  // Using CAN speed
	    bool is_wet_condition = (HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_12) == GPIO_PIN_RESET);
	    process_speed_control(speed, is_wet_condition, false);
	}

	// get RPM

	start_p = end_p + 1;
	end_p = strstr(start_p, ",");
	if (!end_p) {
		config_mutex_give();
		return 0; // error
	}
	uint16_t rpm = 0;
	len = end_p - start_p;
	if (len > 0) {
		memset(buff, 0, sizeof(buff));
		memcpy(buff, start_p, len);
		num = atoi((char const*) buff);
		rpm = (uint16_t) num;

		if (rpm < 0 || rpm > 7000) {

			config_mutex_give();
			return 0;
		}
	}

	check_engine_idle_state(speed, rpm);

	// get TEMPERATURE
	for (uint8_t i = 0; i < 2; i++) { // 2, because speed parameter after 9 other params
		start_p = end_p + 1;
		end_p = strstr(start_p, ",");
		if (end_p == NULL) {

			config_mutex_give();
			return 0; // error
		}
	}

	int engine_temperature = 0;
	len = end_p - start_p;
	if (len > 0) {
		memset(buff, 0, sizeof(buff));
		memcpy(buff, start_p, len);
		num = atoi((char const*) buff);
		engine_temperature = (int) num;

		if (engine_temperature < -10 || engine_temperature > 150) {

			config_mutex_give();
			return 0;
		}
	}

	// get GAS PEDAL KICKDOWN COUNTER
	for (uint8_t i = 0; i < 11; i++) { // 11, because GAS PEDAL KICKDOWN COUNTER is after 10 other params
		start_p = end_p + 1;
		end_p = strstr(start_p, ",");
		if (end_p == NULL) {
			config_mutex_give();
			return 0; // error
		}
	}

	int32_t gasKickdownCntr = 0;
	len = end_p - start_p;
	if (len > 0) {
		memset(buff, 0, sizeof(buff));
		memcpy(buff, start_p, len);
		num = atoi((char const*) buff);
		gasKickdownCntr = num;
	}

	// get TURBO PRESSURE

	for (uint8_t i = 0; i < 2; i++) { // 2, because TURBO PRESSURE is after 1 other params
		start_p = end_p + 1;
		end_p = strstr(start_p, ",");
		if (end_p == NULL) {
			end_p = strchr(start_p, '\x00');
			if (!end_p) {

				config_mutex_give();
				return 0;
			}
			break;
		}

	}

	uint32_t turboPressure = 0;
	len = end_p - start_p;
	if (len > 0) {
		//if (len > 4)
		//	return;
		memset(buff, 0, sizeof(buff));
		memcpy(buff, start_p, len);
		char *p = strchr((char*) &buff[0], '.');
		if (p) {
			if (p == (char*) &buff[0]) {
				config_mutex_give();
				return; // error
			}
			*p = 0;
		}
		num = atoi((char const*) buff);
		turboPressure = num;

		if (turboPressure < 0 || turboPressure > 500) {

			config_mutex_give();
			return 0;
		}
		//Alarme de Turbo

		//Fim de Alarme de Turbo
	}

	// Filtro velocidade ou RPM positivos com motor desligado (adicionado por Pedro)
	if (engine_state != 2) {  // Motor desligado (FR1,0 ou FR1,1)
		if (speed != 0 || rpm != 0) {
			// Se velocidade ou RPM diferente de zero, ignora a string
			config_mutex_give();
			return 0;
		}
	}
	//VALIDACOES
	CAN_COM_LOCK();

	if (can_com.odometer > 0 && can_com.odometer == odometer
			&& can_com.fuel_tank > 0
			&& fuel_tank > 0
			&& fuel_tank + config_file->fuel_threshold_out
					< can_com.fuel_tank) {

		uint8_t alarme[13];
		memset(alarme, 0, sizeof alarme);
		strncpy(alarme, "ALM,108,", 8);
		char strcounter[8];
		int tamanho = sprintf(strcounter, "%d", fuel_tank);
		strncat(alarme, strcounter, tamanho);
		strncat(alarme, "*", 1);
		queue_message_put(can_com.rx_queue_message_handle, NULL, 0, alarme,
				strlen(alarme), NULL, 0, 0);

		can_com.fuel_tank = fuel_tank;

	}

	if (can_com.odometer > 0 && can_com.odometer == odometer
			&& can_com.fuel_tank > 0
			&& fuel_tank > 0
			&& fuel_tank > can_com.fuel_tank + config_file->fuel_threshold_in) {

		uint8_t alarme[13];
		memset(alarme, 0, sizeof alarme);
		strncpy(alarme, "ALM,109,", 8);
		char strcounter[8];
		int tamanho = sprintf(strcounter, "%d", fuel_tank);
		strncat(alarme, strcounter, tamanho);
		strncat(alarme, "*", 1);
		queue_message_put(can_com.rx_queue_message_handle, NULL, 0, alarme,
				strlen(alarme), NULL, 0, 0);

		can_com.fuel_tank = fuel_tank;
	}

	if (can_com.odometer != odometer) {
		can_com.odometer = odometer;
		can_com.fuel_tank = fuel_tank;
	}

	can_com.speed = speed;

	//Chamada para os alarmes de velocidade
    if (config_file->speed_type == 1) {  // Using CAN speed
        bool is_wet_condition = (HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_12) == GPIO_PIN_RESET);
        process_speed_control(speed, is_wet_condition);
    }

	if ((rpm >= config_file->rpm_green_min)
			&& (rpm <= config_file->rpm_green_max)) {
		can_com.rpm_green_zone_cntr++;
		config_file->rpm_green_count++;
	} else if ((rpm >= config_file->rpm_yellow_min)
			&& (rpm <= config_file->rpm_yellow_max)) {
		can_com.rpm_yellow_zone_cntr++;
		config_file->rpm_yellow_count++;
	} else if ((rpm >= config_file->rpm_red_min)
			&& (rpm <= config_file->rpm_red_max)) {
		can_com.rpm_red_zone_cntr++;
		config_file->rpm_red_count++;
	}

	if (rpm > config_file->rpm_max && can_com.rpm_limit_alarm == false) {

		can_com.rpm_limit_alarm = true;

		uint8_t alarme[13];
		memset(alarme, 0, sizeof alarme);
		strncpy(alarme, "ALM,112,", 8);
		char strcounter[8];
		int tamanho = sprintf(strcounter, "%lu", rpm);
		strncat(alarme, strcounter, tamanho);
		strncat(alarme, "*", 1);
		queue_message_put(can_com.rx_queue_message_handle, NULL, 0, alarme,
				strlen(alarme),
				NULL, 0, 0);

		uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF, 0x06, 0x0F, 0x00, 0x0E,
				0x01, 0xEF };

		queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
				hexBytes_audio_driver, sizeof hexBytes_audio_driver,
				NULL, 0, 0);
	} else {
		if (rpm < config_file->rpm_max) {
			can_com.rpm_limit_alarm = false;
		}

	}

	if (config_file->speed_type == 0) {
		speed = gps_com_get_speed_gps();
	}

	// check neutral downhill situation

	if ((rpm < config_file->downhill_rpm_max)
			&& (speed > config_file->downhill_speed_min)) {
		// check neutral downhill start timeout
		if (can_com.neutral_downhill_start_cntr
				< config_file->downhill_seconds_min) {
			// stil start timeout
			can_com.neutral_downhill_start_cntr++;
		} else {
			// start timeout is finished - increase neutral downhill cntr
			if (engine_state = 2 && config_file->downhill_enabled == 1) {
				can_com.neutral_downhill_cntr++;
				config_file->downhill_count++;

			}

			if (can_com.neutral_downhill_cntr
					- can_com.neutral_downhill_cntr_alarm > 10) {

				can_com.neutral_downhill_cntr_alarm =
						can_com.neutral_downhill_cntr;

				char strcounter[16];
				memset(strcounter, 0, 16);

				int tamanho = sprintf(strcounter, "%lu",
						can_com.neutral_downhill_cntr);

				uint8_t alarme[26];
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,107,", 8);
				strncat(alarme, strcounter, tamanho);
				strncat(alarme, "*", 1);
				queue_message_put(can_com.rx_queue_message_handle, NULL, 0,
						alarme, strlen(alarme), NULL, 0, 0);
			}
		}
	} else {
		// clr neutral downhill start timeout
		can_com.neutral_downhill_start_cntr = 0;
	}

	if (config_file->temperature_engine_max < engine_temperature) {
		if (can_com.engine_alarme_on == false) {

			can_com.engine_alarme_on = true;
			uint8_t alarme[13];
			memset(alarme, 0, sizeof alarme);
			strncpy(alarme, "ALM,111,", 8);
			char strcounter[8];
			int tamanho = sprintf(strcounter, "%lu", engine_temperature);
			strncat(alarme, strcounter, tamanho);
			strncat(alarme, "*", 1);
			queue_message_put(can_com.rx_queue_message_handle, NULL, 0, alarme,
					strlen(alarme),
					NULL, 0, 0);

			uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF, 0x06, 0x0F, 0x00,
					0x05, 0x01, 0xEF, };

			queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
					hexBytes_audio_driver, sizeof hexBytes_audio_driver,
					NULL, 0, 0);
		}
	} else {
		can_com.engine_alarme_on = false;
	}

	if (can_com.gasKickdownFirst) {
		can_com.gasKickdownFirst = false;
		can_com.gasKickdownCntr = gasKickdownCntr;
	} else {
		if (gasKickdownCntr != can_com.gasKickdownCntr) {
			can_com.gasKickdownCntr = gasKickdownCntr;
		}
	}

	if (turboPressure > config_file->turbo_pressure_max) {
		if (can_com.turbo_Time < config_file->turbo_count) {
			can_com.turbo_Time++;
		} else {
			if (!can_com.turboPressureOverLimitWarn) {
				can_com.turboPressureOverLimitWarn = true;
				uint8_t alarme[13];
				memset(alarme, 0, sizeof alarme);
				strncpy(alarme, "ALM,106,", 8);
				char strcounter[8];
				int tamanho = sprintf(strcounter, "%lu", turboPressure);
				strncat(alarme, strcounter, tamanho);
				strncat(alarme, "*", 1);
				queue_message_put(can_com.rx_queue_message_handle, NULL, 0,
						alarme, strlen(alarme), NULL, 0, 0);

				uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF, 0x06, 0x0F,
						0x00, 0x11, 0x01, 0xEF };

				queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL,
						0, hexBytes_audio_driver, sizeof hexBytes_audio_driver,
						NULL, 0, 0);
			}

			can_com.turboPressureOverLimitDuration++;
			config_file->turbo_count++;
		}
	} else {
		can_com.turboPressureOverLimitWarn = false;
		can_com.turbo_Time++;
	}

	CAN_COM_UNLOCK();
	config_mutex_give();

	return 1;
}

static void dispatch(can_com_rx_mail_t *mail_p) {

// log CAN RX data

	audio_debug_com_send_log(mail_p->buff, mail_p->size,
	PERIPHERAL_UART_CAN_NUM, false);

// remove special symbols from the message
	uint8_t *payload_start_p = mail_p->buff;
	size_t payload_size = mail_p->size;
	size_t payload_size_new = 0;
	uint8_t i;

	// Check if payload is empty
	if (payload_size == 0) {

		__disable_irq();
		if (can_com.idle_timeout_prev_ms == 0)
			can_com.idle_timeout_ms = 0;	// prev is 0 - not need to put [END]
		else
			can_com.idle_timeout_ms = can_com.idle_timeout_prev_ms;	// restore prev timeout
		__enable_irq();

		// free mail
		queue_mail_free(can_com.rx_queue_mail_handle, mail_p);
		return;
	}

	//validar se a string contem o numero certo de campos (22 virgulas)
	uint8_t countCheck = 0;

    for (i = 0; i < strlen(payload_start_p); ++i) {
        if (payload_start_p[i] == ',') {
            countCheck++;
        }
    }

    bool is_regular_message = (countCheck == 22 &&
                               payload_start_p[0] == 'F' &&
                               payload_start_p[1] == 'R' &&
                               payload_start_p[2] == '1');

    config_file_t *config_file = config_file_get();
    config_mutex_take(osWaitForever);

    if (is_regular_message) {
        // Processa mensagem convencional de 22 virgulas

        // speed value conversion
        if (config_file->pulse_enabled > 0) {
            payload_size_new = 0;
            if (can_com_speed_value_replace(payload_start_p, payload_size,
                    mail_p->buff,
                    CAN_UART_RX_BUFF_SIZE /* mail_p->size*/, &payload_size_new)) {
                payload_start_p = mail_p->buff;
                payload_size = payload_size_new;
                payload_start_p[payload_size] = '\0';
                if (payload_size == 0) {
                    __disable_irq();
                    if (can_com.idle_timeout_prev_ms == 0) {
                        // prev is 0 - not need to put [END]
                        can_com.idle_timeout_ms = 0;
                    } else {
                        //  restore prev timeout
                        can_com.idle_timeout_ms = can_com.idle_timeout_prev_ms;
                    }
                    __enable_irq();
                    // free mail
                    queue_mail_free(can_com.rx_queue_mail_handle, mail_p);
                    config_mutex_give();
                    return;
                }
            }
        }

        // parse message from CAN
        if (can_com_parse_message(payload_start_p, payload_size) == 0) {
            queue_mail_free(can_com.rx_queue_mail_handle, mail_p);
        } else {
            // put message to message queue
            if (config_file->pulse_enabled > 0) {
                // do not send message start any more
                // clr idle timeout
                __disable_irq();
                can_com.idle_timeout_ms = 0;
                __enable_irq();
            } else {
                payload_start_p[strcspn(payload_start_p, "\r\n")] = 0;
                int finalSize =
                        snprintf(payload_start_p, payload_size,
                                "%s,%u,%u,%u,%d,%u,%u,%u,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d",
                                payload_start_p, can_com.rpm_green_zone_cntr,
                                can_com.rpm_yellow_zone_cntr,
                                can_com.rpm_red_zone_cntr,
                                can_com.neutral_downhill_cntr, rpm_get_rpm_freq(),
                                rpm_get_speed_freq(),
                                rpm_get_odometer_ablosute_cnt(),
                                tilt_sensor_get_roll(), tilt_sensor_get_pitch(),
                                tilt_sensor_get_tpm(),
                                tilt_sensor_get_rollover_alarm() ? 1 : 0,
                                (can_com.in3 ? 1 : 0), (can_com.in4 ? 1 : 0),
                                can_com.turboPressureOverLimitDuration,
                                (can_com.in5 ? 1 : 0), (can_com.in6 ? 1 : 0),
                                (can_com.in7 ? 1 : 0), (can_com.in8 ? 1 : 0),
                                (can_com.in9 ? 1 : 0), (can_com.in10 ? 1 : 0),
                                (can_com.out2 ? 1 : 0), (can_com.out3 ? 1 : 0),
                                (can_com.out4 ? 1 : 0), (can_com.out5 ? 1 : 0));
                queue_message_put(can_com.rx_queue_message_handle,
                NULL, 0, (void*) payload_start_p, (size_t) finalSize,
                NULL, 0, osWaitForever);
                // check for footer
                queue_mail_free(can_com.rx_queue_mail_handle, mail_p);
            }
        }
    } else {
        // Processa mensagem nao convencional
        const char* valid_prefixes[] = {"CAR", "LIMITS", "REBOOT", "VERSION", "@MODE", "CONFIG", "DEBUG", "RAPIDS"};
        //const char* valid_prefixes[] = {""};
        int num_prefixes = sizeof(valid_prefixes) / sizeof(valid_prefixes[0]);
        bool is_valid_non_regular = false;

        for (int j = 0; j < num_prefixes; j++) {
            if (strncmp(payload_start_p, valid_prefixes[j], strlen(valid_prefixes[j])) == 0) {
                is_valid_non_regular = true;
                break;
            }
        }

        //Envia nao convencional para fila adicionando prefixo
        if (is_valid_non_regular) {
            char modified_payload[CAN_UART_RX_BUFF_SIZE + 20]; // +20 para prefixo, sufixo e null terminator
            char cleaned_payload[CAN_UART_RX_BUFF_SIZE];
            int cleaned_size = 0;

            // Remove CR and LF characters
            for (int i = 0; payload_start_p[i] != '\0' && cleaned_size < CAN_UART_RX_BUFF_SIZE - 1; i++) {
                if (payload_start_p[i] != '\r' && payload_start_p[i] != '\n') {
                    cleaned_payload[cleaned_size++] = payload_start_p[i];
                }
            }
            cleaned_payload[cleaned_size] = '\0';

            int modified_size = snprintf(modified_payload, sizeof(modified_payload), "TOSERVER,%s*", cleaned_payload);

            if (modified_size > 0 && modified_size < sizeof(modified_payload)) {
                queue_message_put(can_com.rx_queue_message_handle,
                                  NULL, 0, (void*) modified_payload, (size_t) modified_size,
                                  NULL, 0, osWaitForever);
            }
        }
        // descarta se nao for selecionada como nao convencional
        queue_mail_free(can_com.rx_queue_mail_handle, mail_p);
    }

    config_mutex_give();
}


// RX

void can_com_rx_cpl_cb(uint8_t *data_p, size_t data_len) {

	if ((!data_p) || (data_len == 0))
		return;

// refresh idle timeout
	__disable_irq();
	can_com.idle_timeout_prev_ms = can_com.idle_timeout_ms;
	can_com.idle_timeout_ms = CAN_IDLE_TIMEOUT_MS;
	__enable_irq();

// send mail
	queue_mail_put(can_com.rx_queue_mail_handle, data_p, data_len, 0);

}

static void can_com_rx_thread(const void *arg) {

	for (;;) {

		// get mail from CAN RX mail queue
		can_com_rx_mail_t *mail_p = (can_com_rx_mail_t*) queue_mail_get(
				can_com.rx_queue_mail_handle, osWaitForever);

		if (mail_p)
			dispatch(mail_p);

		watchdog_reset();

	} // thread loop

}

// TX

static void can_com_tx_cpl_cb() {
	osSignalSet(can_com.tx_task_handle, CAN_COM_TX_END_SIGNAL);
}

static void can_com_tx_thread(void const *arg) {

	for (;;) {

		// get message from can tx message queue
		queue_message_t *message_p = queue_message_get(
				can_com.tx_queue_message_handle, osWaitForever);

		if (!message_p) {
			watchdog_reset();
			continue;
		}

		// log CAN TX data
		audio_debug_com_send_log(message_p->buff, message_p->size,
		PERIPHERAL_UART_CAN_NUM, false);

		// clr tx end signal
		osSignalWait(CAN_COM_TX_END_SIGNAL, 0);

		// send message
		bool tx_status = can_uart_send(message_p->buff, message_p->size,
				can_com_tx_cpl_cb);

		// wait end of tx
		if (tx_status)
			osSignalWait(CAN_COM_TX_END_SIGNAL, osWaitForever);

		// free message
		queue_message_free(message_p);

		watchdog_reset();
	}

}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
bool can_com_init(void) {
// create mutex
	osMutexDef(can_mutex);
	can_com.access_mux = osMutexCreate(osMutex(can_mutex));
	if (can_com.access_mux == NULL) {
		Error_Handler();
		return false;
	}

// RX
// create rx mail queue
	osMailQDef(rx_mail_queue, CAN_COM_RX_MAILS_QUEUE_SIZE, can_com_rx_mail_t);
	can_com.rx_queue_mail_handle = osMailCreate(osMailQ(rx_mail_queue), NULL);
	if (can_com.rx_queue_mail_handle == NULL) {
		Error_Handler();
		return false;
	}

// create rx message queue
	uint32_t tmp = 0;
	osMessageQDef(rx_message_queue, CAN_COM_RX_MESSAGES_QUEUE_SIZE, &tmp);
	can_com.rx_queue_message_handle = osMessageCreate(
			osMessageQ(rx_message_queue),
			NULL);
	if (can_com.rx_queue_message_handle == NULL) {
		Error_Handler();
		return false;
	}

// create rx task
	osThreadDef(can_rx_task, can_com_rx_thread, osPriorityNormal, 0,
			configMINIMAL_STACK_SIZE * 10);
	can_com.rx_task_handle = osThreadCreate(osThread(can_rx_task), NULL);
	if (can_com.rx_task_handle == NULL) {
		Error_Handler();
		return false;
	}

// TX
// create tx message queue
	osMessageQDef(tx_message_queue, CAN_COM_TX_MESSAGES_QUEUE_SIZE, &tmp);
	can_com.tx_queue_message_handle = osMessageCreate(
			osMessageQ(tx_message_queue),
			NULL);
	if (can_com.tx_queue_message_handle == NULL) {
		Error_Handler();
		return false;
	}

// create tx task
	osThreadDef(can_tx_task, can_com_tx_thread, osPriorityNormal, 0,
			configMINIMAL_STACK_SIZE * 4);
	can_com.tx_task_handle = osThreadCreate(osThread(can_tx_task), NULL);
	if (can_com.tx_task_handle == NULL) {
		Error_Handler();
		return false;
	}

// uart init
	if (!can_uart_init(can_com_rx_cpl_cb)) {
		Error_Handler();
		return false;
	}

	return true;
}

bool can_com_is_idle() {

	__disable_irq();
	bool is_idle = (can_com.idle_timeout_ms == 0);
	__enable_irq();

	return is_idle;
}

void* can_com_get_rx_message(bool *is_idle_p) {

	if (!is_idle_p)
		return 0;

	__disable_irq();

	queue_message_t *message_p = (queue_message_t*) queue_message_get(
			can_com.rx_queue_message_handle, 0);
	*is_idle_p = (can_com.idle_timeout_ms == 0);

	__enable_irq();

	return (void*) message_p;
}

osMessageQId can_com_get_rx_messages_queue() {
	return can_com.rx_queue_message_handle;
}

osMessageQId can_com_get_tx_messages_queue() {
	return can_com.tx_queue_message_handle;
}

void can_com_set_counters(uint32_t green_cntr, uint32_t yellow_cntr,
		uint32_t red_cntr, uint32_t neutral_downhill_cntr) {

	can_com.rpm_green_zone_cntr = green_cntr;
	can_com.rpm_yellow_zone_cntr = yellow_cntr;
	can_com.rpm_red_zone_cntr = red_cntr;
	can_com.neutral_downhill_cntr = neutral_downhill_cntr;
	can_com.neutral_downhill_start_cntr = 0;

}

void can_com_set_turbo_counter(uint32_t pressureOverLimitDuration) {

	can_com.turboPressureOverLimitDuration = pressureOverLimitDuration;
	config_file_t *config_file = config_file_get();
	config_file->turbo_count = pressureOverLimitDuration;
}

void can_com_set_in_stat(int port, int value) {

	if (port == 3)
		can_com.in3 = value;
	else if (port == 4)
		can_com.in4 = value;
	else if (port == 5)
		can_com.in5 = value;
	else if (port == 6)
		can_com.in6 = value;
	else if (port == 7)
		can_com.in7 = value;
	else if (port == 8)
		can_com.in8 = value;
	else if (port == 9)
		can_com.in9 = value;
	else if (port == 10)
		can_com.in10 = value;
}

void can_com_set_out_stat(int port, int value) {

	if (port == 2)
		can_com.out2 = value;
	else if (port == 3)
		can_com.out3 = value;
	else if (port == 4)
		can_com.out4 = value;
	else if (port == 5)
		can_com.out5 = value;
}

void can_com_set_eng_counters(uint64_t movement_timer, uint64_t stopped_timer) {
	can_com.movement_timer = movement_timer;
	can_com.stopped_timer = stopped_timer;
}

void can_com_clr_turbo_counter() {
	can_com.turboPressureOverLimitDuration = 0;
	config_file_t *config_file = config_file_get();
	config_file->turbo_count = 0;
}

uint8_t can_com_get_speed() {
	uint8_t tmp_speed = can_com.speed;
	return tmp_speed;
}

void check_engine_idle_state(uint8_t speed, uint16_t rpm) {
    config_file_t *config_file = config_file_get();

    // Check if the idle engine cut feature is enabled
    if (config_file->idle_engine_cut_enabled != 1) {
        // If the feature is disabled, reset all counters and flags, then return
        engine_idle_counter = 0;
        engine_shutdown_counter = 0;
        engine_shutdown_active = false;
        return;
    }

    bool idle_conditions_met = (speed == 0 && rpm >= 300 && rpm <= 900);

    if (idle_conditions_met) {
        if (!engine_shutdown_active) {
            engine_idle_counter++;
            if (engine_idle_counter >= config_file->idle_engine_cut_timer) {
                // Activate engine shutdown
                engine_shutdown_active = true;
                engine_shutdown_counter = 0;
                HAL_GPIO_WritePin(GPIOC, GPIO_PIN_4, GPIO_PIN_SET);

                // Queue the alarm message
                const char *alarm_msg = "ALM,114,1*";
                queue_message_put(can_com.rx_queue_message_handle, NULL, 0,
                                  (void*)alarm_msg, strlen(alarm_msg), NULL, 0, 0);

                // Queue the audio message
                uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF, 0x06, 0x0F, 0x00, 0x1E, 0x01, 0xEF };
                queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL, 0,
                                  hexBytes_audio_driver, sizeof hexBytes_audio_driver,
                                  NULL, 0, 0);
            }
        } else {
            engine_shutdown_counter++;
            if (engine_shutdown_counter >= config_file->idle_engine_cut_period) {
                // Deactivate engine shutdown after idle_engine_cut_period
                engine_shutdown_active = false;
                HAL_GPIO_WritePin(GPIOC, GPIO_PIN_4, GPIO_PIN_RESET);
                // Reset counters
                engine_idle_counter = 0;
                engine_shutdown_counter = 0;
            }
        }
    } else {
        // Reset everything if conditions are not met
        if (engine_shutdown_active) {
            HAL_GPIO_WritePin(GPIOC, GPIO_PIN_4, GPIO_PIN_RESET);
        }
        engine_idle_counter = 0;
        engine_shutdown_counter = 0;
        engine_shutdown_active = false;
    }
}
