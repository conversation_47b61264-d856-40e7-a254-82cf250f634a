/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _GPS_UART_H_
#define _GPS_UART_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
//#define GPS_UART_IRQn               USART1_IRQn
//#define GPS_UART_IRQHandler         USART1_IRQHandler
//#define GPS_UART_PREEM_PRIO         6
//#define GPS_UART_SUB_PRIO           0
//
////ALTERADO
////#define GPS_UART_DMA_TX_IRQn        DMA1_Channel4_IRQn
//#define GPS_UART_DMA_TX_IRQHandler  DMA1_Channel4_IRQHandler
//#define GPS_UART_DMA_TX_PREEM_PRIO  6
//#define GPS_UART_DMA_TX_SUB_PRIO    0
//
////ALTERADO
////#define GPS_UART_DMA_RX_IRQn        DMA1_Channel5_IRQn
//#define GPS_UART_DMA_RX_IRQHandler  DMA1_Channel5_IRQHandler
//#define GPS_UART_DMA_RX_PREEM_PRIO  6
//#define GPS_UART_DMA_RX_SUB_PRIO    0

#define GPS_UART_RX_BUFF_SIZE       700

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
UART_HandleTypeDef* gps_uart_get(void);

bool gps_uart_init(void (*rx_cpl_cb)(uint8_t* data_p, size_t data_len));
void gps_uart_msp_init(void);
void gps_uart_msp_deinit(void);

bool gps_uart_is_rx_started(void);
void gps_uart_rx_cpl_cb(void);

bool gps_uart_send(uint8_t* data, size_t data_len, void (*tx_cpl_cb)(void));
void gps_uart_tx_cpl_cb(void);

void gps_uart_err_cb(void);

void GPS_UART_DMA_RX_IRQHandler(void);
void GPS_UART_DMA_TX_IRQHandler(void);
void GPS_UART_IRQHandler(void);

#endif // _GPS_UART_H_
