/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "queue_message.h"
//#include "log.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define TAG "QUEUE_MESSAGE"

/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Private Data
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Public Functions
 ****************************************************************************/
bool  queue_message_put(osMessageQId queue_id,
                        void* header_p, size_t header_size,
                        void* message_p, size_t message_size,
                        void* footer_p, size_t footer_size,
                        uint32_t timeout)
{
  if ((message_p == NULL) || (message_size == 0) || (queue_id == NULL) ||
      ((header_size > 0) && (header_p == NULL)) ||
      ((footer_size > 0) && (footer_p == NULL))) {
    //LOG(TAG, "queue_mail_put() - Wrong params!");
    return false;
  }

  size_t queue_mess_size = header_size + message_size + footer_size + sizeof(size_t);
  queue_message_t *queue_mess_p = (queue_message_t *)pvPortMalloc(queue_mess_size);
  if (queue_mess_p == NULL) {
    //LOG(TAG, "queue_mail_put() - Can't allocate memory!");
    return false;
  }

  memset(queue_mess_p, 0, queue_mess_size);
  queue_mess_p->size = header_size + message_size + footer_size;

  // add header
  size_t position = 0;
  if (header_size > 0) {
    memcpy(&queue_mess_p->buff[position], header_p, header_size);
    position += header_size;
  }

  // add mssage
  memcpy(&queue_mess_p->buff[position], message_p, message_size);
  position += message_size;

  // add footer
  if (footer_size > 0) {
    memcpy(&queue_mess_p->buff[position], footer_p, footer_size);
    position += footer_size;
  }

  if (osMessagePut(queue_id, (uint32_t)queue_mess_p, timeout) != osOK) {
    vPortFree(queue_mess_p);
    // LOG(TAG, "queue_mail_put() - Can't put message!");
    return false;
  }

  return true;
}

void* queue_message_get(osMessageQId queue_id, uint32_t timeout)
{
  if (queue_id == NULL) {
    // LOG(TAG, "queue_mail_get() - Wrong params!");
    return NULL;
  }

  osEvent event = osMessageGet(queue_id, timeout);
  if (event.status == osEventMessage) {
    return event.value.p;
  }

  return (void*)NULL;
}

void queue_message_clr(osMessageQId queue_id)
{
  if (queue_id == NULL) {
    //LOG(TAG, "queue_mail_clr() - Wrong params!");
    return;
  }

  void* pointer = NULL;
  while ((pointer = queue_message_get(queue_id, 0)) != NULL) {
    queue_message_free(pointer);
    pointer = NULL;
  }
}

void queue_message_free(void* message)
{
  if (message == NULL) {
    // LOG(TAG, "queue_mail_free() - Wrong params!");
    return;
  }

  vPortFree(message);
}
