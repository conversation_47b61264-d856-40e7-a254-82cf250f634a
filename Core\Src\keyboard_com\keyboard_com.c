/**
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: <PERSON><PERSON><PERSON>
 **/

/**
 * Included Files
 **/
#include "keyboard_com.h"
#include "../drivers/uart/keyboard_uart.h"
#include "../audio_debug_com/audio_debug_com.h"
#include "../gps_com/gps_com.h"

#include "../queue_mail/queue_mail.h"
#include "../queue_message/queue_message.h"

#include "../config/config.h"
#include "../watchdog/watchdog.h"

/**
 * Pre-processor Definitions
 **/
#define KEYBOARD_COM_RX_MAILS_QUEUE_SIZE         (15)  // Hold incoming mails from PERIPH5 till RX task processing it. So, should be not big(processing is fast)
#define KEYBOARD_COM_TX_MESSAGES_QUEUE_SIZE      (2)  // Hold incoming messages from GPS till uart is send data. So, should be not big(sending operation is fast)

#define KEYBOARD_COM_TX_END_SIGNAL               (1<<0)

#define KEYBOARD_IDLE_TIMEOUT_MS                 (15)

/**
 * Private Types
 **/
typedef struct {
    size_t size;
    uint8_t buff[KEYBOARD_UART_RX_BUFF_SIZE];
} keyboard_com_rx_mail_t;

typedef struct {
    osThreadId rx_task_handle;           // rx task handle(receive from periph5)
    osMailQId rx_queue_mail_handle;  // rx mails from irq(received from periph5)

    osThreadId tx_task_handle;             // tx task handle(send to periph5)
    osMessageQId tx_queue_message_handle;  // tx messages queue(send to periph5)

    osThreadId process_task_handle;

    uint8_t idle_timeout_ms;           // idle timeout for periph5 communication

    uint8_t keyboard_message[KEYBOARD_UART_RX_BUFF_SIZE];  // sensor message
    uint8_t keyboard_message_len;                         // message len

    bool driver_id_turn;
    bool active_code_turn;

    bool driver_id_registred;
    bool active_code_registred;

    bool ignition_changing_off;

    char driver_id[11];
    char active_code[2];

    osThreadId ignition_monitor_handle;
    volatile bool keyboard_power_on;
    volatile uint32_t keyboard_power_on_time;
    volatile bool ignition_on;
    volatile bool sms_display_mode;
    volatile uint32_t sms_display_mode_start_time;

} keyboard_com_t;
/**
 * Private Function Prototypes
 **/
// RX
void keyboard_com_rx_cpl_cb(uint8_t *data_p, size_t data_len);
static void keyboard_com_rx_thread(void const *argument);

// TX
static void keyboard_com_tx_cpl_cb(void);
static void keyboard_com_tx_thread(void const *argument);

static void keyboard_com_ignition_monitor_thread(void const *argument);
static void keyboard_power_control(bool power_on);

/**
 * Private Data
 **/
static keyboard_com_t keyboard_com = {0};
static bool process_started = false;
static char data_load[11];
static bool first_ignition_cycle = true;
static bool audio_played = false;

/**
 * Public Data
 **/
bool driver_is_identified = false;
/**
 * Private Functions
 **/
// RX
void keyboard_com_rx_cpl_cb(uint8_t *data_p, size_t data_len) {
    if ((data_p == NULL) || (data_len == 0)) {
        return;
    }
    if (data_len > KEYBOARD_UART_RX_BUFF_SIZE) {
        data_len = KEYBOARD_UART_RX_BUFF_SIZE;
    }

    keyboard_com.idle_timeout_ms = KEYBOARD_IDLE_TIMEOUT_MS;

    queue_mail_put(keyboard_com.rx_queue_mail_handle, data_p, data_len, 0);
}

static void keyboard_com_check_process(void const *argument) {

    while (1) {
        if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_15) == GPIO_PIN_RESET) {
            config_file_t *config_file = config_file_get();

            if (!process_started && (keyboard_com.driver_id_turn == true || first_ignition_cycle)) {
                if (!audio_played) {
                    uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF, 0x06, 0x0F,
                            0x00, 0x01, 0x01, 0xEF };

                    queue_message_put(audio_debug_com_get_tx_messages_queue(), NULL,
                            0, hexBytes_audio_driver, sizeof hexBytes_audio_driver,
                            NULL, 0, 0);
                    audio_played = true;
                }

                process_started = true;
                first_ignition_cycle = false;
                keyboard_com.driver_id_turn = true;
                keyboard_com.active_code_turn = false;

                uint8_t hexBytes_keyboard_driver[] = { 0x1B, 0x43, 0x30, 0x0D,
                        0x0A, 0x1B, 0x53, 0x30, 0x20, 0x49, 0x4E, 0x53, 0x49,
                        0x52, 0x41, 0x20, 0x4F, 0x20, 0x43, 0x50, 0x46, 0x0D,
                        0x0A };

                queue_message_put(keyboard_com_get_tx_messages_queue(),
                NULL, 0, hexBytes_keyboard_driver,
                        sizeof hexBytes_keyboard_driver,
                        NULL, 0, 0);

            } else if (!process_started && keyboard_com.active_code_turn == true
                    && config_file->keyboard_type == 0) {

                process_started = true;

                uint8_t hexBytes_keyboard_activity[] = { 0x1B, 0x43, 0x30, 0x0D,
                        0x0A, 0x1B, 0x53, 0x30, 0x49, 0x4E, 0x53, 0x49, 0x52,
                        0x41, 0x20, 0x41, 0x54, 0x49, 0x56, 0x49, 0x44, 0x41,
                        0x44, 0x45, 0x0D, 0x0A };

                queue_message_put(keyboard_com_get_tx_messages_queue(), NULL, 0,
                        hexBytes_keyboard_activity,
                        sizeof hexBytes_keyboard_activity,
                        NULL, 0, 0);
            }
        } else {
            audio_played = false;
        }
        osDelay(1);
    }
}

static void keyboard_com_rx_thread(void const *argument) {
    keyboard_com_rx_mail_t *mail_p = NULL;

    while (1) {
        mail_p = (keyboard_com_rx_mail_t*) queue_mail_get(
                keyboard_com.rx_queue_mail_handle, osWaitForever);
        if (mail_p != NULL) {

            config_file_t *config_file = config_file_get();

            // processing of incomming data
            if (strlen(mail_p->buff) == 12 && keyboard_com.driver_id_turn
                    && config_file->keyboard_type == 0) {

                // log keyboard RX data
                audio_debug_com_send_log(mail_p->buff, 11,
                PERIPHERAL_UART_KEYBOARD_NUM, true);

                //Validation driver!
                config_mutex_take(osWaitForever);
                for (int index_comma = 0;
                        index_comma < sizeof config_file->array_driver_id;
                        ++index_comma) {
                    if (strlen(
                            config_file->array_driver_id[index_comma].driver_id)
                            != 0) {
                        if (strncmp(
                                config_file->array_driver_id[index_comma].driver_id,
                                mail_p->buff, 11) == 0) {

                            strncpy(keyboard_com.driver_id, mail_p->buff, 11);
                            keyboard_com.driver_id_registred = true;
                            keyboard_com.driver_id_turn = false;
                            keyboard_com.active_code_turn = true;

                            process_started = false;

                            break;
                        }
                    } else {

                        uint8_t hexBytes_keyboard_activity[] = { 0x1B, 0x43,
                                0x30, 0x0D, 0x0A, 0x1B, 0x53, 0x30, 0x45, 0x52,
                                0x52, 0x4F, 0x2C, 0x20, 0x52, 0x45, 0x50, 0x49,
                                0x54, 0x41, 0x0D, 0x0A };

                        queue_message_put(keyboard_com_get_tx_messages_queue(),
                        NULL, 0, hexBytes_keyboard_activity,
                                sizeof hexBytes_keyboard_activity,
                                NULL, 0, 0);

                        keyboard_com.driver_id_registred = false;

                        break;
                    }
                }
            } else if (strlen(mail_p->buff) == 3
                    && keyboard_com.active_code_turn == true
                    && config_file->keyboard_type == 0) {

                audio_debug_com_send_log(mail_p->buff, 2,
                PERIPHERAL_UART_KEYBOARD_NUM, true);

                //Validation driver!
                config_file_t *config_file = config_file_get();
                config_mutex_take(osWaitForever);

                for (int index_comma = 0;
                        index_comma < sizeof config_file->array_activity_code;
                        ++index_comma) {
                    if (strlen(
                            config_file->array_activity_code[index_comma].activity_code)
                            != 0) {
                        if (strncmp(
                                config_file->array_activity_code[index_comma].activity_code,
                                mail_p->buff, 2) == 0) {

                            strncpy(keyboard_com.active_code, mail_p->buff, 2);
                            keyboard_com.active_code_registred = true;
                            keyboard_com.driver_id_turn = false;
                            keyboard_com.active_code_turn = false;

                            uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF,
                                    0x06, 0x0F, 0x00, 0x03, 0x01, 0xEF };

                            queue_message_put(
                                    audio_debug_com_get_tx_messages_queue(),
                                    NULL, 0, hexBytes_audio_driver,
                                    sizeof hexBytes_audio_driver,
                                    NULL, 0, 0);

                            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14,
                                    GPIO_PIN_RESET);

                            uint8_t retorno[23];
                            memset(retorno, 0, sizeof retorno);
                            strncpy(retorno, "DRV,", 4);
                            strncat(retorno, keyboard_com.driver_id, 11);
                            strncat(retorno, ",ACT,", 5);
                            strncat(retorno, keyboard_com.active_code, 2);
                            strncat(retorno, "*", 1);

                            queue_message_put(gps_com_get_tx_messages_queue(),
                            NULL, 0, retorno, sizeof retorno,
                            NULL, 0, 0);

                            process_started = false;

                            break;
                        }
                    } else {

                        uint8_t hexBytes_keyboard_activity[] = { 0x1B, 0x43,
                                0x30, 0x0D, 0x0A, 0x1B, 0x53, 0x30, 0x45, 0x52,
                                0x52, 0x4F, 0x2C, 0x20, 0x52, 0x45, 0x50, 0x49,
                                0x54, 0x41, 0x0D, 0x0A };

                        queue_message_put(keyboard_com_get_tx_messages_queue(),
                        NULL, 0, hexBytes_keyboard_activity,
                                sizeof hexBytes_keyboard_activity,
                                NULL, 0, 0);

                        keyboard_com.active_code_registred = false;

                        break;
                    }
                }
            } else if (strlen(mail_p->buff) > 0
                    && (keyboard_com.active_code_turn == true
                            || keyboard_com.driver_id_turn == true)
                    && config_file->keyboard_type == 0) {
                uint8_t hexBytes_keyboard_activity[] = { 0x1B, 0x43, 0x30, 0x0D,
                        0x0A, 0x1B, 0x53, 0x30, 0x45, 0x52, 0x52, 0x4F, 0x2C,
                        0x20, 0x52, 0x45, 0x50, 0x49, 0x54, 0x41, 0x0D, 0x0A };

                queue_message_put(keyboard_com_get_tx_messages_queue(), NULL, 0,
                        hexBytes_keyboard_activity,
                        sizeof hexBytes_keyboard_activity,
                        NULL, 0, 0);

                keyboard_com.driver_id_registred = false;
                keyboard_com.active_code_registred = false;
            } else if (strlen(mail_p->buff) == 14
                    && keyboard_com.driver_id_turn == true
                    && config_file->keyboard_type == 1) {

                // log keyboard RX data
                audio_debug_com_send_log(mail_p->buff, 14,
                PERIPHERAL_UART_KEYBOARD_NUM, true);

                uint8_t data[11];

                strncpy(data, mail_p->buff + 2, 11);

                //Validation driver!
                config_mutex_take(osWaitForever);
                for (int index_comma = 0;
                        index_comma < sizeof config_file->array_driver_id;
                        ++index_comma) {
                    if (strlen(
                            config_file->array_driver_id[index_comma].driver_id)
                            != 0) {
                        if (strncmp(
                                config_file->array_driver_id[index_comma].driver_id,
                                data, 11) == 0) {

                            osDelay(1000);
                            strncpy(keyboard_com.driver_id, data, 11);
                            strncpy(keyboard_com.active_code, "12", 2);

                            keyboard_com.driver_id_registred = true;
                            keyboard_com.driver_id_turn = false;
                            keyboard_com.active_code_turn = false;

                            keyboard_com.active_code_registred = true;
                            keyboard_com.driver_id_turn = false;
                            keyboard_com.active_code_turn = false;

                            uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF,
                                    0x06, 0x0F, 0x00, 0x03, 0x01, 0xEF };

                            queue_message_put(
                                    audio_debug_com_get_tx_messages_queue(),
                                    NULL, 0, hexBytes_audio_driver,
                                    sizeof hexBytes_audio_driver,
                                    NULL, 0, 0);

                            HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14,
                                    GPIO_PIN_RESET);

                            uint8_t retorno[23];
                            memset(retorno, 0, sizeof retorno);
                            strncpy(retorno, "DRV,", 4);
                            strncat(retorno, keyboard_com.driver_id, 11);
                            strncat(retorno, ",ACT,", 5);
                            strncat(retorno, keyboard_com.active_code, 2);
                            strncat(retorno, "*", 1);

                            queue_message_put(gps_com_get_tx_messages_queue(),
                            NULL, 0, retorno, sizeof retorno,
                            NULL, 0, 0);

                            process_started = false;

                            break;
                        }
                    }
                }
            } else if (strlen(mail_p->buff)
                    == 1&& keyboard_com.driver_id_turn == false && keyboard_com.active_code_turn == false) {
                HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_RESET);
            }

            if (keyboard_com.sms_display_mode && strlen(mail_p->buff) == 1) {
                // User pressed a key (likely OK) to acknowledge the SMS
                keyboard_com.sms_display_mode = false;
                if (!keyboard_com.ignition_on) {
                    // Power off the keyboard if ignition is off
                    keyboard_power_control(false);
                }
            }

            if (keyboard_com.driver_id_registred && keyboard_com.active_code_registred) {
                update_driver_identification_status();
            }

            // free mail
            queue_mail_free(keyboard_com.rx_queue_mail_handle, mail_p);
            mail_p = NULL;
            config_mutex_give();

            watchdog_reset();
        }

        config_mutex_give();
    }
}

static void keyboard_com_tx_thread(void const *argument) {
    queue_message_t *message_p = NULL;
    bool tx_status = false;

    while (1) {
        message_p = queue_message_get(keyboard_com.tx_queue_message_handle, 10);  // Short timeout

        if (message_p != NULL) {
            // Check if keyboard is powered on and initialized, or ready for SMS display
            if ((keyboard_com.keyboard_power_on &&
                 (osKernelSysTick() - keyboard_com.keyboard_power_on_time) >= 2000) ||
                keyboard_is_ready_for_sms_display()) {

                if (!keyboard_uart_init(keyboard_com_rx_cpl_cb)) {
                    Error_Handler();
                    queue_message_free(message_p);
                    continue;
                }

                osSignalWait(KEYBOARD_COM_TX_END_SIGNAL, 0);

                tx_status = keyboard_uart_send(message_p->buff, message_p->size);

                if (!tx_status) {
                    // Handle send failure (e.g., re-queue the message)
                    queue_message_put(keyboard_com.tx_queue_message_handle, NULL, 0, message_p->buff, message_p->size, NULL, 0, 0);
                }
            } else {
                // Re-queue the message if keyboard is not ready
                queue_message_put(keyboard_com.tx_queue_message_handle, NULL, 0, message_p->buff, message_p->size, NULL, 0, 0);
            }

            queue_message_free(message_p);
            message_p = NULL;
        }

        watchdog_reset();
    }
}

void update_driver_identification_status(void) {
    driver_is_identified = keyboard_com.driver_id_registred &&
                           keyboard_com.active_code_registred &&
                           keyboard_com.ignition_on;
}

// Add this new function
static void keyboard_power_control(bool power_on) {
    if ((power_on && !keyboard_com.keyboard_power_on) || keyboard_com.sms_display_mode) {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_SET);
        keyboard_com.keyboard_power_on = true;
        keyboard_com.keyboard_power_on_time = osKernelSysTick();
    } else if (!power_on && keyboard_com.keyboard_power_on && !keyboard_com.sms_display_mode) {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_RESET);
        keyboard_com.keyboard_power_on = false;
    }
}

void keyboard_enter_sms_display_mode(void) {
    keyboard_com.sms_display_mode = true;
    keyboard_com.sms_display_mode_start_time = osKernelSysTick();
    keyboard_power_control(true);
}

bool keyboard_is_ready_for_sms_display(void) {
    return keyboard_com.sms_display_mode &&
           (osKernelSysTick() - keyboard_com.sms_display_mode_start_time >= 2000);
}

static void keyboard_com_ignition_monitor_thread(void const *argument) {
    bool previous_ignition_state = keyboard_com.ignition_on;

    while (1) {
        bool current_ignition_state = (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_15) == GPIO_PIN_RESET);

        if (current_ignition_state != previous_ignition_state) {
            keyboard_com.ignition_on = current_ignition_state;
            keyboard_power_control(keyboard_com.ignition_on);

            update_driver_identification_status();

            // Clear the audio message queue before adding new messages
            queue_message_clr(audio_debug_com_get_tx_messages_queue());

            if (keyboard_com.ignition_on) {
                keyboard_start_process();
                //HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);  // Turn on CANBUS module
            } else {
                keyboard_off_ignition();
                //HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);  // Turn off CANBUS module

                // Trigger goodbye audio message
                uint8_t hexBytes_audio_driver[] = { 0x7E, 0xFF, 0x06, 0x0F, 0x00, 0x02, 0x01, 0xEF };
                queue_message_put(audio_debug_com_get_tx_messages_queue(),
                    NULL, 0, hexBytes_audio_driver, sizeof hexBytes_audio_driver,
                    NULL, 0, 0);

                // Save configuration when ignition turns off
                save_config();
            }

            previous_ignition_state = current_ignition_state;
        }

        osDelay(10);  // Check every 10ms
    }
}

/**
 * Public Functions
 **/
bool keyboard_com_init(void) {
// RX
// create rx mail queue
    osMailQDef(rx_mail_queue, KEYBOARD_COM_RX_MAILS_QUEUE_SIZE,
            keyboard_com_rx_mail_t);
    keyboard_com.rx_queue_mail_handle = osMailCreate(osMailQ(rx_mail_queue),
    NULL);
    if (keyboard_com.rx_queue_mail_handle == NULL) {
        Error_Handler();
        return false;
    }

// create rx task
    osThreadDef(keyboard_rx_task, keyboard_com_rx_thread, osPriorityNormal, 0,
            configMINIMAL_STACK_SIZE*2);
    keyboard_com.rx_task_handle = osThreadCreate(osThread(keyboard_rx_task),
    NULL);
    if (keyboard_com.rx_task_handle == NULL) {
        Error_Handler();
        return false;
    }

// create tx message queue
    uint32_t tmp = 0;
    osMessageQDef(tx_message_queue, KEYBOARD_COM_TX_MESSAGES_QUEUE_SIZE, &tmp);
    keyboard_com.tx_queue_message_handle = osMessageCreate(
            osMessageQ(tx_message_queue),
            NULL);
    if (keyboard_com.tx_queue_message_handle == NULL) {
        Error_Handler();
        return false;
    }

// create tx task
    osThreadDef(keyboard_tx_task, keyboard_com_tx_thread, osPriorityNormal, 0,
            configMINIMAL_STACK_SIZE*2);
    keyboard_com.tx_task_handle = osThreadCreate(osThread(keyboard_tx_task),
    NULL);
    if (keyboard_com.tx_task_handle == NULL) {
        Error_Handler();
        return false;
    }

    osThreadDef(keyboard_process_task, keyboard_com_check_process,
            osPriorityNormal, 0, configMINIMAL_STACK_SIZE*20);
    keyboard_com.process_task_handle = osThreadCreate(
            osThread(keyboard_process_task),
            NULL);
    if (keyboard_com.process_task_handle == NULL) {
        Error_Handler();
        return false;
    }

    // Create ignition monitor task
    osThreadDef(keyboard_ignition_monitor_task, keyboard_com_ignition_monitor_thread, osPriorityNormal, 0, configMINIMAL_STACK_SIZE);
    keyboard_com.ignition_monitor_handle = osThreadCreate(osThread(keyboard_ignition_monitor_task), NULL);
    if (keyboard_com.ignition_monitor_handle == NULL) {
        Error_Handler();
        return false;
    }

    driver_is_identified = false;

    return true;
}

bool keyboard_com_get_ignition_state(void) {
    return keyboard_com.ignition_on;
}

bool keyboard_com_is_idle(void) {
    if ((keyboard_com.idle_timeout_ms == 0)) {
        return true;
    }
    return false;
}

osMessageQId keyboard_com_get_tx_messages_queue(void) {
    return keyboard_com.tx_queue_message_handle;
}

void keyboard_start_process(void) {
    keyboard_com.driver_id_turn = true;
    keyboard_com.active_code_turn = false;
    keyboard_com.driver_id_registred = false;
    keyboard_com.active_code_registred = false;
    process_started = false;
    memset(keyboard_com.driver_id, 0, 11);
    memset(keyboard_com.active_code, 0, 2);

    update_driver_identification_status();
}

void keyboard_off_ignition(void) {
    keyboard_com.ignition_changing_off = true;
    first_ignition_cycle = true;
    audio_played = false;
    // Remove the goodbye audio message trigger from here
}
