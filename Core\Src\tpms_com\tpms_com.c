/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "tpms_com.h"

#include "../drivers/uart/tpms_uart.h"
#include "../queue_mail/queue_mail.h"
#include "../queue_message/queue_message.h"
#include "../config/config.h"
#include "../audio_debug_com/audio_debug_com.h"
#include "../tpms_com/tpms_com.h"
#include "../watchdog/watchdog.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define TPMS_COM_RX_MAILS_QUEUE_SIZE         (10)  // Hold incoming mails from TPMS till RX task processing it. So, should be not big(processing is fast)
#define TPMS_COM_RX_MESSAGES_QUEUE_SIZE      (10) // Hold incoming messages(wrapped) from TPMS till GPS task processing data from other peripherals. So, should be big enough.
#define TPMS_COM_TX_MESSAGES_QUEUE_SIZE      (10)  // Hold incoming messages from GPS till uart is send data. So, should be not big(sending operation is fast)

#define TPMS_ID_PART_1			7
#define TPMS_ID_PART_2			8
#define TPMS_ID_PART_3			10
#define TPMS_ID_PART_4			11
#define TPMS_ID_PART_5			13
#define TPMS_ID_PART_6			14

#define TPMS_PRESSURE_PART_1  	16
#define TPMS_PRESSURE_PART_2  	17

#define TPMS_TEMP_PART_1  	    19
#define TPMS_TEMP_PART_2  		20

#define TPMS_BAT_PART_1  	 	22
#define TPMS_BAT_PART_2  		23

#define TPMS_CHECKSUM  			26

#define TPMS_COM_TX_END_SIGNAL               (1<<0)

#define TPMS_IDLE_TIMEOUT_MS                 (15)

#define TPMS_COM_LOCK()                      osMutexWait(tpms_com.access_mux, osWaitForever)
#define TPMS_COM_UNLOCK()                    osMutexRelease(tpms_com.access_mux)
/****************************************************************************
 * Private Types
 ****************************************************************************/
typedef struct {
	size_t size;
	uint8_t buff[TPMS_UART_RX_BUFF_SIZE + 1];
} tpms_com_rx_mail_t; // Incoming mail from TPMS

typedef struct {
	osThreadId rx_task_handle;             // rx task handle(receive from tpms)
	osMailQId rx_queue_mail_handle;     // rx mails from irq(received from tpms)
	osMessageQId rx_queue_message_handle; // rx messages(received from tpms and processed)

	osThreadId tx_task_handle;             // tx task handle(send to tpms)
	osMessageQId tx_queue_message_handle;    // tx messages queue(send to tpms)

	bool is_end_inserted;            // flag for [END] insertion indication
	bool is_end_tx_wait;             // flag for [END] tx waiting indication
	uint16_t idle_timeout_ms;            // idle timeout for tpms communication
	uint16_t idle_timeout_prev_ms;       // prev value of idle timeout

	uint8_t tmp_message_buff[TPMS_UART_RX_BUFF_SIZE + 1]; // tmp message buffer
	char end_message_buff[27];                     // end message
	char emulated_message[TPMS_UART_RX_BUFF_SIZE + 1]; // emulated message

	osMutexId access_mux;                  // access mutex for some TPMS context

	char sensor_id[12];
	int alarm;
	int pressure;
	int temperature;

} tpms_com_t;

// Forward declarations

static bool tpms_message_parse(uint8_t *message_p, uint8_t message_len);
static uint16_t tpms_checksum_calc(uint8_t *message_p, uint8_t message_len);
// RX
void tpms_com_rx_cpl_cb(uint8_t *data_p, size_t data_len);
static void tpms_com_rx_thread(void const *argument);

// TX
static void tpms_com_tx_cpl_cb(void);
static void tpms_com_tx_thread(void const *argument);

/****************************************************************************
 * Private Data
 ****************************************************************************/
static tpms_com_t tpms_com = { };

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

static void dispatch(tpms_com_rx_mail_t *mail_p) {

	// log TPMS RX data
	if (strstr((char*) mail_p->buff, PERIPHERAL_CMD_END) == NULL) {
		audio_debug_com_send_log(mail_p->buff, mail_p->size,
		PERIPHERAL_UART_TPMS_NUM, false);
	}

	char message[50];
	memset(message, 0, sizeof message);

	snprintf(message, (sizeof(message)), "%s,%.12s|%u|%u|%u", "TPMS",
			tpms_com.sensor_id, tpms_com.alarm, tpms_com.pressure,
			tpms_com.temperature);

	int i = 0;
	for (i = 0; i < strlen(message); ++i) {
		if (message[i] == 0)
			break;
	}

	message[i] = '*';

	TPMS_COM_LOCK();
	__disable_irq();
	if (tpms_com.idle_timeout_prev_ms == 0)
		tpms_com.idle_timeout_ms = 0;	// prev is 0 - not need to put [END]
	else
		tpms_com.idle_timeout_ms = tpms_com.idle_timeout_prev_ms;// restore prev timeout
	__enable_irq();

	queue_message_put(tpms_com.rx_queue_message_handle,
	NULL, 0, message, (i + 1),
	NULL, 0, 0);

	TPMS_COM_UNLOCK();

	// free mail
	queue_mail_free(tpms_com.rx_queue_mail_handle, mail_p);
}

// RX

void tpms_com_rx_cpl_cb(uint8_t *data_p, size_t data_len) {

	if ((!data_p) || (data_len == 0))
		return;

	if (data_len > TPMS_UART_RX_BUFF_SIZE)
		data_len = TPMS_UART_RX_BUFF_SIZE;

	// refresh idle timeout
	__disable_irq();
	tpms_com.idle_timeout_prev_ms = tpms_com.idle_timeout_ms;
	tpms_com.idle_timeout_ms = TPMS_IDLE_TIMEOUT_MS;
	__enable_irq();

	// send mail
	queue_mail_put(tpms_com.rx_queue_mail_handle, data_p, data_len, 0);
}

static void tpms_com_rx_thread(const void *arg) {

	for (;;) {

		// get mail from TPMS RX mail queue
		tpms_com_rx_mail_t *mail_p = (tpms_com_rx_mail_t*) queue_mail_get(
				tpms_com.rx_queue_mail_handle, osWaitForever);

		if (mail_p) {
			if (tpms_message_parse(mail_p->buff, mail_p->size)) {
				dispatch(mail_p);
			} else {
				queue_mail_free(tpms_com.rx_queue_mail_handle, mail_p);
			}
		}

		watchdog_reset();

	} // thread loop

}

static bool tpms_message_parse(uint8_t *message_p, uint8_t message_len) {
	// check in params
	if ((message_p == NULL) || (message_len != TPMS_UART_RX_BUFF_SIZE)) {
		// message is not correct
		return false;
	}

	// checksum check
	uint16_t checksum = ((uint16_t) tpms_checksum_calc(message_p,
			(message_len - 1)));
	if (((uint16_t) checksum) != ((uint16_t) message_p[TPMS_CHECKSUM])) {
		// wrong checksum
		return false;
	}

	tpms_com.alarm = 0;

	//TESTE TEMPERATURA
	uint8_t temperature =
			(signed short) ((((uint16_t) message_p[TPMS_TEMP_PART_1]) << 8)
					| message_p[TPMS_TEMP_PART_2]);
	temperature = temperature - 50;

	if (temperature > 150 || temperature < -10) {
		return false;
	}
	//Parse final
	uint8_t pressure =
			(signed short) ((((uint16_t) message_p[TPMS_PRESSURE_PART_1]) << 8)
					| message_p[TPMS_PRESSURE_PART_2]);

	pressure = pressure * 0.74;

	tpms_com.pressure = pressure;
	tpms_com.temperature = temperature;

	uint8_t battery = (signed short) ((((uint16_t) message_p[TPMS_BAT_PART_1])
			<< 8) | message_p[TPMS_BAT_PART_2]);

	if (battery == 0) {
		tpms_com.alarm = 4;
	}

	char strSensorId[12];

	uint16_t sensor_id_part_1 = ((((uint16_t) message_p[TPMS_ID_PART_1]) << 8)
			| message_p[TPMS_ID_PART_2]);

	uint16_t sensor_id_part_2 = ((((uint16_t) message_p[TPMS_ID_PART_3]) << 8)
			| message_p[TPMS_ID_PART_4]);

	uint16_t sensor_id_part_3 = ((((uint16_t) message_p[TPMS_ID_PART_5]) << 8)
			| message_p[TPMS_ID_PART_6]);

	sprintf(strSensorId, "%4X%4X%4X", sensor_id_part_1, sensor_id_part_2,
			sensor_id_part_3);

	for (int i = 0; i < strlen(strSensorId); i++) {
		if (strSensorId[i] == 32) {
			strSensorId[i] = '0';
		}
	}

	strncpy(tpms_com.sensor_id, strSensorId, 12);

	//AQUI VAMOS VERIFICAR A TABELA
	config_file_t *config_file = config_file_get();
	config_mutex_take(osWaitForever);
	for (int index_comma = 0; index_comma < sizeof config_file->array_tpms;
			++index_comma) {

		if (strlen(config_file->array_tpms[index_comma].code) != 0) {
			if (strncmp(tpms_com.sensor_id,
					config_file->array_tpms[index_comma].code, 12) == 0) {
				if (config_file->array_tpms[index_comma].temperature_max
						< temperature
						|| config_file->array_tpms[index_comma].temperature_min
								> temperature) {
					tpms_com.alarm += 2;
				}
				if (config_file->array_tpms[index_comma].pressure_max < pressure
						|| config_file->array_tpms[index_comma].pressure_min
								> pressure) {
					tpms_com.alarm += 1;
				}

				config_mutex_give();
				return true;
			}
		} else {
			break;
		}
	}
	config_mutex_give();
	return false;
}

static uint16_t tpms_checksum_calc(uint8_t *message_p, uint8_t message_len) {
	if ((message_p == NULL) || (message_len == 0)) {
		// message is not correct
		return 0x0000;
	}

	if (((uint16_t) message_p[4]) == 0x00A7) {
		return ((message_p[TPMS_ID_PART_1]) + (message_p[TPMS_ID_PART_2])
				+ (message_p[TPMS_ID_PART_3]) + (message_p[TPMS_ID_PART_4])
				+ (message_p[TPMS_ID_PART_5]) + (message_p[TPMS_ID_PART_6])
				+ (message_p[TPMS_PRESSURE_PART_1])
				+ (message_p[TPMS_PRESSURE_PART_2])
				+ (message_p[TPMS_TEMP_PART_1]) + (message_p[TPMS_TEMP_PART_2])
				+ (0x77)) % 256;
	} else if (((uint16_t) message_p[4]) == 0x0001) {
		return ((0x40) + (message_p[TPMS_ID_PART_2])
				+ (message_p[TPMS_ID_PART_3]) + (message_p[TPMS_ID_PART_4])
				+ (message_p[TPMS_ID_PART_5]) + (message_p[TPMS_ID_PART_6])
				+ (message_p[TPMS_PRESSURE_PART_1])
				+ (message_p[TPMS_PRESSURE_PART_2])
				+ (message_p[TPMS_TEMP_PART_1]) + (message_p[TPMS_TEMP_PART_2])
				+ (0x77)) % 256;
	} else if (((uint16_t) message_p[4]) == 0x0002) {
		return ((0x80) + (message_p[TPMS_ID_PART_2])
				+ (message_p[TPMS_ID_PART_3]) + (message_p[TPMS_ID_PART_4])
				+ (message_p[TPMS_ID_PART_5]) + (message_p[TPMS_ID_PART_6])
				+ (message_p[TPMS_PRESSURE_PART_1])
				+ (message_p[TPMS_PRESSURE_PART_2])
				+ (message_p[TPMS_TEMP_PART_1]) + (message_p[TPMS_TEMP_PART_2])
				+ (0x77)) % 256;
	}

	return 0x0000;
}
// TX
/****************************************************************************
 * Public Functions
 ****************************************************************************/
bool tpms_com_init(void) {
	// create mutex
	osMutexDef(tpms_mutex);
	tpms_com.access_mux = osMutexCreate(osMutex(tpms_mutex));
	if (tpms_com.access_mux == NULL) {
		Error_Handler();
		return false;
	}

	// RX
	// create rx mail queue
	osMailQDef(rx_mail_queue, TPMS_COM_RX_MAILS_QUEUE_SIZE, tpms_com_rx_mail_t);
	tpms_com.rx_queue_mail_handle = osMailCreate(osMailQ(rx_mail_queue), NULL);
	if (tpms_com.rx_queue_mail_handle == NULL) {
		Error_Handler();
		return false;
	}

	// create rx message queue
	uint32_t tmp = 0;
	osMessageQDef(rx_message_queue, TPMS_COM_RX_MESSAGES_QUEUE_SIZE, &tmp);
	tpms_com.rx_queue_message_handle = osMessageCreate(
			osMessageQ(rx_message_queue), NULL);
	if (tpms_com.rx_queue_message_handle == NULL) {
		Error_Handler();
		return false;
	}

	// create rx task
	osThreadDef(tpms_rx_task, tpms_com_rx_thread, osPriorityNormal, 0,
			configMINIMAL_STACK_SIZE * 2);
	tpms_com.rx_task_handle = osThreadCreate(osThread(tpms_rx_task), NULL);
	if (tpms_com.rx_task_handle == NULL) {
		Error_Handler();
		return false;
	}

	// uart init
	if (!tpms_uart_init(tpms_com_rx_cpl_cb)) {
		Error_Handler();
		return false;
	}

	return true;
}

void* tpms_com_get_rx_message(bool *is_idle_p) {

	if (!is_idle_p)
		return 0;

	TPMS_COM_LOCK();
	__disable_irq();

	queue_message_t *message_p = (queue_message_t*) queue_message_get(
			tpms_com.rx_queue_message_handle, 0);
	*is_idle_p = (tpms_com.idle_timeout_ms == 0)
			&& (tpms_com.is_end_tx_wait == false);

	__enable_irq();
	TPMS_COM_UNLOCK();

	return (void*) message_p;
}

osMessageQId tpms_com_get_rx_messages_queue() {
	return tpms_com.rx_queue_message_handle;
}

osMessageQId tpms_com_get_tx_messages_queue(void) {
	return tpms_com.tx_queue_message_handle;
}

