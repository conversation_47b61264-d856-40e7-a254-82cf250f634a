/****************************************************************************
 *   Copyright (C) 2019.12.02. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "queue_mail.h"
//#include "log.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define TAG "QUEUE_MAIL"

/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Private Data
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Public Functions
 ****************************************************************************/
bool queue_mail_put(osMailQId queue_id, void* mail_p, size_t mail_size, uint32_t timeout)
{
  if ((mail_p == NULL) || (mail_size == 0) || (queue_id == NULL)) {
    //LOG(TAG, "queue_mail_put() - Wrong params!");
    return false;
  }

  queue_mail_t* queue_mail_p = (queue_mail_t *)osMailAlloc(queue_id, timeout);
  if (queue_mail_p == NULL) {
    //LOG(TAG, "queue_mail_put() - Can't allocate memory!");
    return false;
  }

  queue_mail_p->size = mail_size;
  memcpy(queue_mail_p->buff, mail_p, mail_size);
  queue_mail_p->buff[mail_size] = '\0';

  if (osMailPut(queue_id, queue_mail_p) != osOK) {
    osMailFree(queue_id, queue_mail_p);
    //LOG(TAG, "queue_mail_put() - Can't put message!");
    return false;
  }

  return true;
}

void* queue_mail_get(osMailQId queue_id, uint32_t timeout)
{
  if (queue_id == NULL) {
    //LOG(TAG, "queue_mail_get() - Wrong params!");
    return NULL;
  }

  osEvent event = osMailGet(queue_id, timeout);
  if (event.status == osEventMail) {
    return event.value.p;
  }

  return NULL;
}

void queue_mail_clr(osMailQId queue_id)
{
  if (queue_id == NULL) {
    //LOG(TAG, "queue_mail_clr() - Wrong params!");
    return;
  }

  void* pointer = NULL;
  while ((pointer = queue_mail_get(queue_id, 0)) != NULL) {
    queue_mail_free(queue_id, pointer);
    pointer = NULL;
  }
}

void queue_mail_free(osMailQId queue_id, void* message)
{
  if ((queue_id == NULL) || (message == NULL)) {
    //LOG(TAG, "queue_mail_free() - Wrong params!");
    return;
  }

  osMailFree(queue_id, message);
}