/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "gps_uart.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define UART_NUMBER                 USART1

#define gps_uart_IRQHandler         USART1_IRQHandler

#define UART_CLK_ENABLE()           __HAL_RCC_USART1_CLK_ENABLE()
#define UART_CLK_DISABLE()          __HAL_RCC_USART1_CLK_DISABLE()

#define UART_GPIO_CLK_ENABLE()      __HAL_RCC_GPIOA_CLK_ENABLE()

#define UART_DMA_CLK_ENABLE()       __HAL_RCC_DMA1_CLK_ENABLE()

/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void);

/****************************************************************************
 * Private Data
 ****************************************************************************/
UART_HandleTypeDef huart1;
static void (*tx_cpl_cb_p)(void) = NULL;

static void (*rx_cpl_cb_p)(uint8_t *data_p, size_t data_len) = NULL;
static uint8_t rx_data[GPS_UART_RX_BUFF_SIZE];

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void) {
	__disable_irq();

	HAL_StatusTypeDef status = HAL_UARTEx_ReceiveToIdle_DMA(&huart1, rx_data,
			sizeof(rx_data));

	__enable_irq();

	return status;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
UART_HandleTypeDef* gps_uart_get(void) {
	return &huart1;
}

bool gps_uart_init(void (*rx_cpl_cb)(uint8_t *data_p, size_t data_len)) {

	huart1.Instance = USART1;
	huart1.Init.BaudRate = 115200;
	huart1.Init.WordLength = UART_WORDLENGTH_8B;
	huart1.Init.StopBits = UART_STOPBITS_1;
	huart1.Init.Parity = UART_PARITY_NONE;
	huart1.Init.Mode = UART_MODE_TX_RX;
	huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	huart1.Init.OverSampling = UART_OVERSAMPLING_16;
	if (HAL_UART_Init(&huart1) != HAL_OK) {
		Error_Handler();
	}
	// start rx
	rx_cpl_cb_p = rx_cpl_cb;
	if (uart_start_rx() != HAL_OK) {
		return false;
	}

	return true;
}

bool gps_uart_send(uint8_t *data_p, size_t data_len, void (*tx_cpl_cb)(void)) {
	if ((data_p == NULL) || (data_len == 0)) {
		return false;
	}
	tx_cpl_cb_p = tx_cpl_cb;
	if (HAL_UART_Transmit_IT(&huart1, data_p, data_len) != HAL_OK) {
		tx_cpl_cb_p = NULL;
		return false;
	}
	return true;
}

bool gps_uart_is_rx_started(void) {
	if ((huart1.RxState == HAL_UART_STATE_BUSY_RX)
			&& (huart1.hdmarx->Instance->NDTR < GPS_UART_RX_BUFF_SIZE)) {
		return true;
	}
	return false;
}

void gps_uart_rx_cpl_cb(void) {
	if (rx_cpl_cb_p != NULL) {
		// calculate rx len
		size_t rx_len = huart1.hdmarx->Instance->NDTR;
		if (rx_len > GPS_UART_RX_BUFF_SIZE) {
			rx_len = GPS_UART_RX_BUFF_SIZE;
		}
		rx_len = GPS_UART_RX_BUFF_SIZE - rx_len;

		// notify about new data
		if (rx_len > 0) { // check to prevent execute cp second time
			if (strncmp(rx_data, "CMD,", 4) == 0
					|| strncmp(rx_data, "\r\CMD,", 6) == 0) {
				rx_cpl_cb_p(rx_data, rx_len);
			}
			if (strncmp(rx_data, "GNGGA,", 6) == 0
					|| strncmp(rx_data, "\r\nGNGGA,", 8) == 0) {
				rx_cpl_cb_p(rx_data, rx_len);
			}
			if (strncmp(rx_data, "GNRMC,", 6) == 0
					|| strncmp(rx_data, "\r\nGNRMC,", 8) == 0) {
				rx_cpl_cb_p(rx_data, rx_len);
			}

			memset(rx_data, 0, sizeof rx_data);
		}
	}

	// start rx again
	uart_start_rx();
}

void gps_uart_tx_cpl_cb(void) {
	__disable_irq();

	if (tx_cpl_cb_p != NULL) {
		tx_cpl_cb_p();
		tx_cpl_cb_p = NULL;
	}

	__enable_irq();
}

void gps_uart_err_cb(void) {
	// abort
	__HAL_UART_FLUSH_DRREGISTER(&huart1);
	HAL_UART_Abort(&huart1);

	// do tx_cpl_cb to unlock any waiting tasks
	gps_uart_tx_cpl_cb();

	// start rx again
	uart_start_rx();
}

void gps_uart_IRQHandler(void) {
	// check for idle irq
	bool is_idle = false;
	if (__HAL_UART_GET_FLAG(&huart1, UART_FLAG_IDLE)) {
		is_idle = true;
	}

	// irq processing
	HAL_UART_IRQHandler(&huart1);

	// process idle irq
	if (is_idle) {
		__HAL_UART_CLEAR_IDLEFLAG(&huart1);
		HAL_UART_AbortReceive(&huart1);
		HAL_UART_RxCpltCallback(&huart1);
	}
}
