/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _TILT_SENSOR_UART_H_
#define _TILT_SENSOR_UART_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"


/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define TILT_SENSOR_UART_RX_BUFF_SIZE       300

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
UART_HandleTypeDef* tilt_sensor_uart_get(void);

bool tilt_sensor_uart_init(void (*rx_cpl_cb)(uint8_t* data_p, size_t data_len));
bool tilt_sensor_uart_reinit();
void tilt_sensor_uart_msp_init(void);
void tilt_sensor_uart_msp_deinit(void);

bool tilt_sensor_uart_is_rx_started(void);
void tilt_sensor_uart_rx_cpl_cb(void);

bool tilt_sensor_uart_send(uint8_t* data, size_t data_len, void (*tx_cpl_cb)(void));
void tilt_sensor_uart_tx_cpl_cb(void);

void tilt_sensor_uart_err_cb(void);

void tilt_sensor_uart_IRQHandler(void);

#endif // _TILT_SENSOR_UART_H_
