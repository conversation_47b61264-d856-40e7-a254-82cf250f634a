/****************************************************************************
 *   Copyright (C) 2020.01.22. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

#ifndef _CONFIG_H_
#define _CONFIG_H_

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "main.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define SUBSECTOR_ERASE_CMD                  0x20
#define SECTOR_ERASE_CMD                     0xD8
#define BULK_ERASE_CMD                       0xC7
#define PROG_ERASE_RESUME_CMD                0x7A
#define PROG_ERASE_SUSPEND_CMD               0x75
#define EXT_QUAD_IN_FAST_PROG_CMD            0x32
#define BUFFERSIZE                 (sizeof(aTxDefaultConfig) - 1)

/* Write Operations */
#define WRITE_ENABLE_CMD                     0x06
#define WRITE_DISABLE_CMD                    0x04

/* Register Operations */
#define READ_STATUS_REG_CMD                  0x05
#define WRITE_STATUS_REG_CMD                 0x01

#define WRITE_VOL_CFG_REG_CMD                0x81

#define READ_VOL_CFG_REG_CMD                 0x85

#define DUMMY_CLOCK_CYCLES_READ_QUAD         10

/****************************************************************************
 * Public Types
 ****************************************************************************/
typedef union {
	uint32_t all;
	struct {
		uint32_t speed_conversion :1;   // speed conversion flag
		uint32_t pulse_conversion :1;   // pulse conversion flag
		uint32_t spare :30;  // spare bits
	} fields;
} discrete_t;

typedef struct {
	char driver_id[11];
} driver_id;

typedef struct {
	char activity_code[2];
} activity_code;

typedef struct {
	char code[12];
	uint8_t pressure_min;
	uint8_t pressure_max;
	uint8_t temperature_min;
	uint8_t temperature_max;
} tpms;

typedef struct {
	char code[4];
	uint8_t in_out; //1-out/0-in
	uint8_t speed_max;
	double central_point_lat;
	double central_point_long;
	uint16_t radius;
} geofence_circular;

typedef struct {
	char code[4];
	uint8_t in_out; //1-out/0-in
	uint8_t speed_max;
	double upper_left_point_lat;
	double upper_left_point_long;
	double lower_right_point_lat;
	double lower_right_point_long;
} geofence_rectangular;

typedef struct {
	bool loaded;

	uint32_t rpm_green_count;
	uint32_t rpm_yellow_count;
	uint32_t rpm_red_count;
	uint32_t downhill_count;
	uint32_t turbo_count;
	uint32_t odometer_count;
	uint32_t stopped_timer;
	uint32_t movement_timer;

	uint16_t rpm_green_min;
	uint16_t rpm_green_max;
	uint16_t rpm_yellow_min;
	uint16_t rpm_yellow_max;
	uint16_t rpm_red_min;
	uint16_t rpm_red_max;

	uint16_t downhill_rpm_max;
	uint16_t downhill_speed_min;
	uint16_t downhill_seconds_min;

	uint16_t rpm_max;

	uint16_t temperature_engine_max;
	uint16_t turbo_pressure_max;

	uint32_t uart2_speed;
	uint8_t uart2_bits;
	uint8_t uart2_stop_bit;
	char uart2_flow_control[1];

	uint32_t uart3_speed;
	uint8_t uart3_bits;
	uint8_t uart3_stop_bit;
	char uart3_flow_control[1];

	uint32_t uart4_speed;
	uint8_t uart4_bits;
	uint8_t uart4_stop_bit;
	char uart4_flow_control[1];

	uint32_t uart5_speed;
	uint8_t uart5_bits;
	uint8_t uart5_stop_bit;
	char uart5_flow_control[1];

	uint32_t uart6_speed;
	uint8_t uart6_bits;
	uint8_t uart6_stop_bit;
	char uart6_flow_control[1];

	uint8_t pulse_enabled;
	float pulse_rpm_factor;
	float pulse_speed_factor;

	uint8_t speed_type; //0- GPS, 1- CANBUS

	uint8_t speed_1_speed_beep;
	uint8_t speed_1_speed_alarms;
	uint8_t speed_1_seconds_min;
	uint8_t speed_1_activate_out2;

	uint8_t speed_2_speed_beep;
	uint8_t speed_2_speed_alarms;
	uint8_t speed_2_seconds_min;
	uint8_t speed_2_activate_out2;

	uint8_t downhill_enabled;

	uint8_t fuel_threshold_out;
	uint8_t fuel_threshold_in;

	uint16_t roll_max;

	uint8_t in3_enabled;
	uint8_t in3_notify_when_enabled;
	uint8_t in3_notify_when_disabled;
	uint8_t in3_delay_time;
	uint8_t in3_debounce_time;

	uint8_t in4_enabled;
	uint8_t in4_notify_when_enabled;
	uint8_t in4_notify_when_disabled;
	uint8_t in4_delay_time;
	uint8_t in4_debounce_time;

	uint8_t in5_enabled;
	uint8_t in5_notify_when_enabled;
	uint8_t in5_notify_when_disabled;
	uint8_t in5_delay_time;
	uint8_t in5_debounce_time;

	uint8_t in6_enabled;
	uint8_t in6_notify_when_enabled;
	uint8_t in6_notify_when_disabled;
	uint8_t in6_delay_time;
	uint8_t in6_debounce_time;

	uint8_t in7_enabled;
	uint8_t in7_notify_when_enabled;
	uint8_t in7_notify_when_disabled;
	uint8_t in7_delay_time;
	uint8_t in7_debounce_time;

	uint8_t in8_enabled;
	uint8_t in8_notify_when_enabled;
	uint8_t in8_notify_when_disabled;
	uint8_t in8_delay_time;
	uint8_t in8_debounce_time;

	uint8_t in9_enabled;
	uint8_t in9_notify_when_enabled;
	uint8_t in9_notify_when_disabled;
	uint8_t in9_delay_time;
	uint8_t in9_debounce_time;

	uint8_t in10_enabled;
	uint8_t in10_notify_when_enabled;
	uint8_t in10_notify_when_disabled;
	uint8_t in10_delay_time;
	uint8_t in10_debounce_time;

	uint8_t out2_enabled;
	uint8_t out2_time;

	uint8_t out3_enabled;
	uint8_t out3_time;

	uint8_t out4_enabled;
	uint8_t out4_time;

	uint8_t out5_enabled;
	uint8_t out5_time;

	uint8_t keyboard_type; //0 NORMAL - 1 RFID

	driver_id array_driver_id[1000];

	activity_code array_activity_code[50];

	tpms array_tpms[36];

	geofence_circular array_geofence_circular[50];

	geofence_rectangular array_geofence_rectangular[50];

	uint8_t fw_version;
	uint8_t unidentified_driver_alarm_enabled;

	uint8_t idle_engine_cut_enabled;
	uint8_t idle_engine_cut_timer;
	uint8_t idle_engine_cut_period;

} config_file_t;

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/
void config_init(void);
bool config_save_current(void);

bool config_mutex_take(uint32_t timeout_ms);
bool config_mutex_give(void);

bool save_config(void);
bool load_config(void);

#endif // _CONFIG_H_
