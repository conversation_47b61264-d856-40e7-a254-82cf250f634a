/****************************************************************************
 *   Copyright (C) 2020.01.03. All rights reserved.
 *   Author: NikolayN
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include "tpms_uart.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/
#define UART_NUMBER                 USART5

#define tpms_uart_IRQHandler         UART5_IRQHandler

#define UART_CLK_ENABLE()           __HAL_RCC_USART2_CLK_ENABLE()
#define UART_CLK_DISABLE()          __HAL_RCC_USART2_CLK_DISABLE()

#define UART_GPIO_CLK_ENABLE()      __HAL_RCC_GPIOA_CLK_ENABLE()

#define UART_DMA_CLK_ENABLE()       __HAL_RCC_DMA1_CLK_ENABLE()

/****************************************************************************
 * Private Types
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static void (*tx_cpl_cb_p)(void) = NULL;

static void (*rx_cpl_cb_p)(uint8_t *data_p, size_t data_len) = NULL;
static uint8_t rx_data[TPMS_UART_RX_BUFF_SIZE];

/****************************************************************************
 * Public Data
 ****************************************************************************/
UART_HandleTypeDef huart5;
/****************************************************************************
 * Private Functions
 ****************************************************************************/
static HAL_StatusTypeDef uart_start_rx(void) {
	__disable_irq();

	HAL_StatusTypeDef status = HAL_UARTEx_ReceiveToIdle_IT(&huart5, rx_data,
			sizeof(rx_data));

	__enable_irq();

	return status;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/
UART_HandleTypeDef* tpms_uart_get(void) {
	return &huart5;
}

bool tpms_uart_init(void (*rx_cpl_cb)(uint8_t *data_p, size_t data_len)) {

	huart5.Instance = UART5;
	huart5.Init.BaudRate = 9600;
	huart5.Init.WordLength = UART_WORDLENGTH_8B;
	huart5.Init.StopBits = UART_STOPBITS_1;
	huart5.Init.Parity = UART_PARITY_NONE;
	huart5.Init.Mode = UART_MODE_RX;
	huart5.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	huart5.Init.OverSampling = UART_OVERSAMPLING_16;
	if (HAL_UART_Init(&huart5) != HAL_OK) {
		Error_Handler();
	}
//  // start rx
	rx_cpl_cb_p = rx_cpl_cb;
	if (uart_start_rx() != HAL_OK) {
		return false;
	}

	return true;
}

bool tpms_uart_reinit() {

	// deinit usart
	if (HAL_UART_DeInit(&huart5) != HAL_OK) {
		return false;
	}

	// apply new setings
	huart5.Instance = UART5;
	huart5.Init.BaudRate = 9600;
	huart5.Init.WordLength = UART_WORDLENGTH_8B;
	huart5.Init.StopBits = UART_STOPBITS_1;
	huart5.Init.Parity = UART_PARITY_NONE;
	huart5.Init.Mode = UART_MODE_TX_RX;
	huart5.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	huart5.Init.OverSampling = UART_OVERSAMPLING_16;
	if (HAL_UART_Init(&huart5) != HAL_OK) {
		Error_Handler();
		return false;
	}

	// start rx
	if (uart_start_rx() != HAL_OK) {
		return false;
	}

	return true;
}

bool tpms_uart_send(uint8_t *data_p, size_t data_len, void (*tx_cpl_cb)(void)) {
	if ((data_p == NULL) || (data_len == 0)) {
		return false;
	}
	tx_cpl_cb_p = tx_cpl_cb;
	if (HAL_UART_Transmit_DMA(&huart5, data_p, data_len) != HAL_OK) {
		tx_cpl_cb_p = NULL;
		return false;
	}
	return true;
}

bool tpms_uart_is_rx_started(void) {
	if ((huart5.RxState == HAL_UART_STATE_BUSY_RX)
			&& (huart5.hdmarx->Instance->NDTR < TPMS_UART_RX_BUFF_SIZE)) {
		return true;
	}
	return false;
}

void tpms_uart_rx_cpl_cb(void) {
	if (rx_cpl_cb_p != NULL) {
		// calculate rx len
		size_t rx_len = huart5.hdmarx->Instance->NDTR;
		if (rx_len > TPMS_UART_RX_BUFF_SIZE) {
			rx_len = TPMS_UART_RX_BUFF_SIZE;
		}
		rx_len = TPMS_UART_RX_BUFF_SIZE - rx_len;

		// notify about new data
		if (rx_len > 0) { // check to prevent execute cp second time
			rx_cpl_cb_p(rx_data, rx_len);
		}
	}

	// start rx again
	uart_start_rx();
}

void tpms_uart_tx_cpl_cb(void) {
	__disable_irq();

	if (tx_cpl_cb_p != NULL) {
		tx_cpl_cb_p();
		tx_cpl_cb_p = NULL;
	}

	__enable_irq();
}

void tpms_uart_err_cb(void) {
	// abort
	__HAL_UART_FLUSH_DRREGISTER(&huart5);
	HAL_UART_Abort(&huart5);

	// do tx_cpl_cb to unlock any waiting tasks
	tpms_uart_tx_cpl_cb();

	// start rx again
	uart_start_rx();
}

void tpms_uart_IRQHandler(void) {
	// check for idle irq
	bool is_idle = false;
	if (__HAL_UART_GET_FLAG(&huart5, UART_FLAG_IDLE)) {
		is_idle = true;
	}

	// irq processing
	HAL_UART_IRQHandler(&huart5);

	// process idle irq
	if (is_idle) {
		__HAL_UART_CLEAR_IDLEFLAG(&huart5);
		HAL_UART_AbortReceive(&huart5);
		HAL_UART_RxCpltCallback(&huart5);
	}
}
